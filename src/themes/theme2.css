@import url('https://fonts.googleapis.com/css2?family=Public+Sans:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@100;200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import 'tailwindcss';


@theme {
	/* Colors */
	--color-brand-50: rgb(255, 238, 205);
	--color-brand-100: rgb(255, 220, 155);
	--color-brand-200: rgb(255, 185, 85);
	--color-brand-300: rgb(255, 145, 25);
	--color-brand-400: rgb(245, 120, 0);
	--color-brand-500: rgb(220, 95, 0);
	--color-brand-600: rgb(185, 75, 15);
	--color-brand-700: rgb(155, 60, 35);
	--color-brand-800: rgb(125, 45, 55);
	--color-brand-900: rgb(95, 35, 75);
	--color-brand-950: rgb(65, 25, 95);
	--color-neutral-0: rgb(20, 18, 25);
	--color-neutral-50: rgb(35, 30, 40);
	--color-neutral-100: rgb(50, 42, 55);
	--color-neutral-200: rgb(75, 62, 80);
	--color-neutral-300: rgb(105, 88, 115);
	--color-neutral-400: rgb(140, 118, 155);
	--color-neutral-500: rgb(175, 148, 185);
	--color-neutral-600: rgb(195, 168, 205);
	--color-neutral-700: rgb(215, 188, 225);
	--color-neutral-800: rgb(230, 208, 240);
	--color-neutral-900: rgb(240, 225, 250);
	--color-neutral-950: rgb(248, 238, 255);
	--color-error-50: rgb(240, 255, 235);
	--color-error-100: rgb(215, 255, 205);
	--color-error-200: rgb(185, 255, 175);
	--color-error-300: rgb(145, 255, 135);
	--color-error-400: rgb(95, 255, 85);
	--color-error-500: rgb(45, 245, 35);
	--color-error-600: rgb(25, 215, 15);
	--color-error-700: rgb(15, 185, 5);
	--color-error-800: rgb(10, 155, 0);
	--color-error-900: rgb(5, 125, 0);
	--color-error-950: rgb(0, 95, 0);
	--color-warning-50: rgb(245, 235, 255);
	--color-warning-100: rgb(225, 205, 255);
	--color-warning-200: rgb(195, 165, 255);
	--color-warning-300: rgb(155, 115, 255);
	--color-warning-400: rgb(115, 65, 255);
	--color-warning-500: rgb(85, 25, 245);
	--color-warning-600: rgb(75, 15, 215);
	--color-warning-700: rgb(65, 10, 185);
	--color-warning-800: rgb(55, 8, 155);
	--color-warning-900: rgb(45, 5, 125);
	--color-warning-950: rgb(35, 0, 95);
	--color-success-50: rgb(255, 245, 240);
	--color-success-100: rgb(255, 225, 215);
	--color-success-200: rgb(255, 195, 175);
	--color-success-300: rgb(255, 155, 125);
	--color-success-400: rgb(255, 105, 65);
	--color-success-500: rgb(245, 65, 15);
	--color-success-600: rgb(215, 45, 5);
	--color-success-700: rgb(185, 35, 0);
	--color-success-800: rgb(155, 25, 0);
	--color-success-900: rgb(125, 15, 0);
	--color-success-950: rgb(95, 5, 0);
	--color-info-50: rgb(255, 250, 235);
	--color-info-100: rgb(255, 240, 205);
	--color-info-200: rgb(255, 225, 165);
	--color-info-300: rgb(255, 205, 115);
	--color-info-400: rgb(255, 180, 55);
	--color-info-500: rgb(245, 155, 5);
	--color-info-600: rgb(215, 135, 0);
	--color-info-700: rgb(185, 115, 0);
	--color-info-800: rgb(155, 95, 0);
	--color-info-900: rgb(125, 75, 0);
	--color-info-950: rgb(95, 55, 0);
	--color-brand-primary: rgb(185, 75, 15);
	--color-default-font: rgb(248, 238, 255);
	--color-subtext-color: rgb(175, 148, 185);
	--color-neutral-border: rgb(75, 62, 80);
	--color-white: rgb(20, 18, 25);
	--color-default-background: rgb(20, 18, 25);

	/* Fonts */
	--text-caption: 12px;
	--text-caption--font-weight: 400;
	--text-caption--letter-spacing: 0em;
	--text-caption--line-height: 16px;
	--text-caption-bold: 12px;
	--text-caption-bold--font-weight: 600;
	--text-caption-bold--letter-spacing: 0em;
	--text-caption-bold--line-height: 16px;
	--text-body: 14px;
	--text-body--font-weight: 400;
	--text-body--letter-spacing: 0em;
	--text-body--line-height: 20px;
	--text-body-bold: 14px;
	--text-body-bold--font-weight: 600;
	--text-body-bold--letter-spacing: 0em;
	--text-body-bold--line-height: 20px;
	--text-heading-3: 16px;
	--text-heading-3--font-weight: 700;
	--text-heading-3--letter-spacing: 0em;
	--text-heading-3--line-height: 20px;
	--text-heading-2: 20px;
	--text-heading-2--font-weight: 700;
	--text-heading-2--letter-spacing: 0em;
	--text-heading-2--line-height: 24px;
	--text-heading-1: 30px;
	--text-heading-1--font-weight: 700;
	--text-heading-1--letter-spacing: 0em;
	--text-heading-1--line-height: 36px;
	--text-monospace-body: 14px;
	--text-monospace-body--font-weight: 400;
	--text-monospace-body--letter-spacing: 0em;
	--text-monospace-body--line-height: 20px;
	--text-custom-text: 14px;
	--text-custom-text--font-weight: 400;
	--text-custom-text--letter-spacing: 0em;
	--text-custom-text--line-height: 20px;

	/* Font families */
	--font-caption: 'Public Sans';
	--font-caption-bold: 'Public Sans';
	--font-body: 'Public Sans';
	--font-body-bold: 'Public Sans';
	--font-heading-3: 'Public Sans';
	--font-heading-2: 'Public Sans';
	--font-heading-1: 'Public Sans';
	--font-monospace-body: 'Roboto Mono';
	--font-custom-text: 'Public Sans';

	/* Box shadows */
	--shadow-sm: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
	--shadow-default: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
	--shadow-md: 0px 4px 16px -2px rgba(0, 0, 0, 0.08), 0px 2px 4px -1px rgba(0, 0, 0, 0.08);
	--shadow-lg: 0px 12px 32px -4px rgba(0, 0, 0, 0.08), 0px 4px 8px -2px rgba(0, 0, 0, 0.08);
	--shadow-overlay: 0px 12px 32px -4px rgba(0, 0, 0, 0.08), 0px 4px 8px -2px rgba(0, 0, 0, 0.08);

	/* Border radiuses */
	--radius-sm: 8px;
	--radius-md: 16px;
	--radius-DEFAULT: 16px;
	--radius-lg: 24px;
	--radius-full: 9999px;

	/* Spacing */
	--spacing-112: 28rem;
	--spacing-144: 36rem;
	--spacing-192: 48rem;
	--spacing-256: 64rem;
	--spacing-320: 80rem;
}
