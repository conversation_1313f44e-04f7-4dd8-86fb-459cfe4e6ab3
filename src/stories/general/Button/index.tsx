import { Button as SubframeButton } from '../../../subframe/components/Button';

interface Props extends React.ButtonHTMLAttributes<HTMLButtonElement> {
	variant?:
		| 'brand-primary'
		| 'brand-secondary'
		| 'brand-tertiary'
		| 'neutral-primary'
		| 'neutral-secondary'
		| 'neutral-tertiary'
		| 'destructive-primary'
		| 'destructive-secondary'
		| 'destructive-tertiary'
		| 'inverse';
	size?: 'large' | 'medium' | 'small';
	children?: React.ReactNode;
	icon?: React.ReactNode;
	iconRight?: React.ReactNode;
	loading?: boolean;
	onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
	className?: string;
}

export const Button = (props: Props) => {
	return <SubframeButton {...props}>{props.children}</SubframeButton>;
};
