import type { <PERSON>a, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Button } from './index';
import React from 'react';

const meta: Meta<typeof Button> = {
	title: 'General/Button',
	component: Button,
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component: 'Common button component for reusing everywhere.',
			},
		},
	},
	tags: ['autodocs'],
	argTypes: {
		size: {
			control: { type: 'select' },
			options: ['large', 'medium', 'small'],
		},
		variant: {
			control: { type: 'select' },
			options: [
				'brand-primary',
				'brand-secondary',
				'brand-tertiary',
				'neutral-primary',
				'neutral-secondary',
				'neutral-tertiary',
				'destructive-primary',
				'destructive-secondary',
				'destructive-tertiary',
				'inverse',
			],
		},
		children: {
			control: { type: 'text' },
		},
		icon: {
			control: false,
		},
		iconRight: {
			control: false,
		},
		loading: {
			control: { type: 'boolean' },
		},
		onClick: { action: 'clicked' },
		className: {
			control: { type: 'text' },
		},
	},
	args: {
		onClick: fn(),
		children: 'Button',
		variant: 'brand-primary',
		size: 'medium',
		loading: false,
	},
	decorators: [
		(Story) => (
			<div style={{ width: 400 }}>
				<Story />
			</div>
		),
	],
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Default: Story = {};
