import { FeatherWorkflow } from '@subframe/core';
import { Pool_BracketCard } from '../../../subframe/components/Pool_BracketCard';

export const PoolBracketCard = () => {
	return (
		<Pool_BracketCard
			icon={<FeatherWorkflow />}
			bracketName="17OR3M1"
			startTime="Starts Fri, 3:00pm"
			bracketType="Best of 3"
			courtLabel="Courts:"
			courtList="26ABC, 27ABC, 28ABC, 29ABC, 30ABC"
			teamLabel="Teams:"
			teamList={
				'CJV 17 National Navy, C2 17-1 National Travis, CUVC 17 Open, RVC 17\n          National, M2VBALL 17 Elite 1, LPV-17U, ECJVC 17 National, Sandhills 17\n          Bri'
			}
			children2={
				<>
					<div className="flex w-full items-center justify-between rounded-t-md border-b border-solid border-neutral-border bg-default-background px-3 pt-3 pb-2">
						<div className="flex items-center gap-2">
							<FeatherWorkflow className="text-heading-3 font-heading-3 text-brand-700" />
							<span className="text-heading-2 font-heading-2 text-default-font">17OR3M1</span>
						</div>
						<div className="flex flex-col items-end">
							<span className="text-body font-body text-brand-600">Starts Fri, 3:00pm</span>
							<span className="text-body font-body text-brand-600">Best of 3</span>
						</div>
					</div>
					<div className="flex w-full flex-col items-start gap-2 rounded-b-md bg-brand-50 px-2 py-2">
						<div className="flex w-full items-start gap-2 rounded-sm border border-solid border-neutral-border bg-default-background px-2 py-2">
							<span className="text-body-bold font-body-bold text-brand-600">Courts:</span>
							<span className="text-body font-body text-neutral-700">
								26ABC, 27ABC, 28ABC, 29ABC, 30ABC
							</span>
						</div>
						<div className="flex w-full items-start gap-2 rounded-sm border border-solid border-neutral-border bg-default-background px-2 py-2">
							<span className="text-body-bold font-body-bold text-brand-600">Teams:</span>
							<span className="text-body font-body text-neutral-700">
								CJV 17 National Navy, C2 17-1 National Travis, CUVC 17 Open, RVC 17 National,
								M2VBALL 17 Elite 1, LPV-17U, ECJVC 17 National, Sandhills 17 Bri
							</span>
						</div>
					</div>
				</>
			}
		>
			<span className="text-body-bold font-body-bold text-brand-600">Teams:</span>
			<span className="text-body font-body text-neutral-700">
				CJV 17 National Navy, C2 17-1 National Travis, CUVC 17 Open, RVC 17 National, M2VBALL 17
				Elite 1, LPV-17U, ECJVC 17 National, Sandhills 17 Bri
			</span>
		</Pool_BracketCard>
	);
};
