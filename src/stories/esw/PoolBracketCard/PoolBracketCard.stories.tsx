import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { PoolBracketCard } from './index';
import React from 'react';

const meta = {
	title: 'ESW/PoolBracketCard',
	component: PoolBracketCard,
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component: 'Common button component for reusing everywhere.',
			},
		},
	},
	tags: ['autodocs'],
	argTypes: {
		variant: { control: 'radio' },
	},
	args: { onClick: fn() },
	decorators: [
		(Story) => (
			<div>
				<Story />
			</div>
		),
	],
} satisfies Meta<typeof PoolBracketCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		children: 'Default',
	},
};
