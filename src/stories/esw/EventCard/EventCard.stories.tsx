import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { EventCard } from './index';
import React from 'react';
const meta = {
	title: 'ESW/EventCard',
	component: EventCard,
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component: 'EventCard component',
			},
		},
	},
	tags: ['autodocs'],
	argTypes: {
		variant: { control: 'radio' },
	},
	args: { onClick: fn() },
	decorators: [
		(Story) => (
			<div>
				<Story />
			</div>
		),
	],
} satisfies Meta<typeof EventCard>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		children: 'Default',
	},
};
