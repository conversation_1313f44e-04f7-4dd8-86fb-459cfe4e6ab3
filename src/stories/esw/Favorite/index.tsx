import { TeamTitle } from '../../../subframe/components/TeamTitle';
import { MatchesHorizontal } from '../../../subframe/components/MatchesHorizontal';
import { TeamHeader } from '../../../subframe/components/TeamHeader';
import { FavoriteToggle } from '../../../subframe/components/FavoriteToggle';
import { DropdownMenu } from '../../../subframe/components/DropdownMenu';
import { FeatherStar } from '@subframe/core';
import { FeatherPlus } from '@subframe/core';
import { FeatherEdit2 } from '@subframe/core';
import { FeatherTrash } from '@subframe/core';
import * as SubframeCore from '@subframe/core';
import { FeatherMoreHorizontal } from '@subframe/core';
import { TeamFooter } from '../../../subframe/components/TeamFooter';
import { FavoriteCollapsed } from '../../../subframe/components/FavoriteCollapsed';

export const Favorite = () => {
	return (
		<FavoriteCollapsed athleteName="Athlete Name here" name="Team Name Here" divisionName="18O">
			<TeamHeader>
				<TeamTitle divisionName="18O" name="Team Name Here" athleteName="Athlete Name here" />
				<MatchesHorizontal
					className="h-auto w-full flex-none"
					match1Time="8:00AM CT GWCC 198"
					match2Time="10:00AM CT 199"
				/>
			</TeamHeader>
			<TeamFooter className="h-auto w-full flex-none">
				<FavoriteToggle value="">
					<FavoriteToggle.Item icon={null} value="96dbd9d2">
						Schedule
					</FavoriteToggle.Item>
					<FavoriteToggle.Item icon={null} value="5d233398">
						Pools/Brackets
					</FavoriteToggle.Item>
					<FavoriteToggle.Item icon={null} value="a859673a">
						Futures
					</FavoriteToggle.Item>
					<SubframeCore.DropdownMenu.Root>
						<SubframeCore.DropdownMenu.Trigger asChild={true}>
							<FavoriteToggle.Item
								className="h-7 w-auto flex-none"
								icon={<FeatherMoreHorizontal />}
								value="ec834d56"
							/>
						</SubframeCore.DropdownMenu.Trigger>
						<SubframeCore.DropdownMenu.Portal>
							<SubframeCore.DropdownMenu.Content
								side="bottom"
								align="end"
								sideOffset={4}
								asChild={true}
							>
								<DropdownMenu>
									<DropdownMenu.DropdownItem icon={<FeatherStar />}>
										Favorite
									</DropdownMenu.DropdownItem>
									<DropdownMenu.DropdownItem icon={<FeatherPlus />}>Add</DropdownMenu.DropdownItem>
									<DropdownMenu.DropdownItem icon={<FeatherEdit2 />}>
										Edit
									</DropdownMenu.DropdownItem>
									<DropdownMenu.DropdownItem icon={<FeatherTrash />}>
										Delete
									</DropdownMenu.DropdownItem>
								</DropdownMenu>
							</SubframeCore.DropdownMenu.Content>
						</SubframeCore.DropdownMenu.Portal>
					</SubframeCore.DropdownMenu.Root>
				</FavoriteToggle>
			</TeamFooter>
		</FavoriteCollapsed>
	);
};
