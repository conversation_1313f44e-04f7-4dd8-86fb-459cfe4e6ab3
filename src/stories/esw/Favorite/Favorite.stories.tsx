import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { Favorite } from './index';

const meta = {
	title: 'ESW/Favorite',
	component: Favorite,
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component: 'Common button component for reusing everywhere.',
			},
		},
	},
	tags: ['autodocs'],
	argTypes: {
		variant: { control: 'radio' },
	},
	args: { onClick: fn() },
	decorators: [
		(Story) => (
			<div>
				<Story />
			</div>
		),
	],
} satisfies Meta<typeof Favorite>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		children: 'Default',
	},
};
