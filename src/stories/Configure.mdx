import { Meta } from '@storybook/blocks';

<Meta title="Introduction" />

# Sportwrench Storybook

This Storybook contains reusable UI components for **Sportwrench** projects.

All components are developed with consistency and scalability in mind, aiming to support multiple products under the Sportwrench ecosystem.

Use this library as the single source of truth for styling, behavior, and interactions across applications.

## Package Information

- **Package Name**: `@sw-web/react-storybook`
- **Current Version**: 0.1.2
- **Registry**: GitLab Private Registry

## Installation

To use this library in your projects, you need to configure access to our private GitLab registry.

### 1. Create .npmrc file

Create a `.npmrc` file in your project root:

```
@sw-web:registry=https://gitlab.uastage.com/api/v4/groups/171/-/packages/npm/
//gitlab.uastage.com/api/v4/groups/171/-/packages/npm/:_authToken=gldt-Lo17oyYk2n25z4FgKCc9
//gitlab.uastage.com/api/v4/projects/231/packages/npm/:_authToken=gldt-Lo17oyYk2n25z4FgKCc9
```

### 2. Install the package

**Using npm:**
```bash
npm install @sw-web/react-storybook
```

**Using yarn:**
```bash
yarn add @sw-web/react-storybook
```

## Authentication

You need a GitLab Personal Access Token with `api` scope:

1. Go to GitLab → User Settings → Access Tokens
2. Create a token with `api` scope
3. Replace the token in your `.npmrc` file or set the environment variable: `export NPM_TOKEN=your_token_here`

## Getting Started

Once installed, you can import and use components in your React application:

```jsx
import { Button, Card } from '@sw-web/react-storybook';

function App() {
  return (
    <div>
      <Button variant="primary">Click me</Button>
      <Card title="Example">Content here</Card>
    </div>
  );
}
```

Browse the components in this Storybook to see all available options, props, and usage examples.
