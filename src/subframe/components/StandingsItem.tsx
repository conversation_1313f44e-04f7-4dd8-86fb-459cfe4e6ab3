'use client';
/*
 * Documentation:
 * standingsItem — https://app.subframe.com/5ed7fb16a283/library?component=standingsItem_7a527ae7-2337-433b-8398-62043c1b2828
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';
import { FeatherStar } from '@subframe/core';

interface StandingsItemRootProps extends React.HTMLAttributes<HTMLDivElement> {
	placement?: React.ReactNode;
	favoriteOption?: React.ReactNode;
	teamName?: React.ReactNode;
	matchScore?: React.ReactNode;
	setScore?: React.ReactNode;
	setPercentage?: React.ReactNode;
	pointPercentage?: React.ReactNode;
	children?: React.ReactNode;
	className?: string;
}

const StandingsItemRoot = React.forwardRef<HTMLElement, StandingsItemRootProps>(
	function StandingsItemRoot(
		{
			placement,
			favoriteOption = <FeatherStar />,
			teamName,
			matchScore,
			setScore,
			setPercentage,
			pointPercentage,
			children,
			className,
			...otherProps
		}: StandingsItemRootProps,
		ref,
	) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex w-full items-center justify-between border-b border-solid border-neutral-border px-4 py-4',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const StandingsItem = StandingsItemRoot;
