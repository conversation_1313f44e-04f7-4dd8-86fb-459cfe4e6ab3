'use client';
/*
 * Documentation:
 * team component — https://app.subframe.com/5ed7fb16a283/library?component=team+component_c7186b19-71c5-4ed9-b0a7-06b9594d41d0
 * Badge — https://app.subframe.com/5ed7fb16a283/library?component=Badge_97bdb082-1124-4dd7-a335-b14b822d0157
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';
import { FeatherFlag } from '@subframe/core';
import { FeatherAward } from '@subframe/core';
import { Badge } from './Badge';

interface TeamComponentRootProps extends React.HTMLAttributes<HTMLDivElement> {
	teamName?: React.ReactNode;
	match1Icon?: React.ReactNode;
	match1?: React.ReactNode;
	match2Icon?: React.ReactNode;
	match2?: React.ReactNode;
	className?: string;
}

const TeamComponentRoot = React.forwardRef<HTMLElement, TeamComponentRootProps>(
	function TeamComponentRoot(
		{
			teamName,
			match1Icon = <FeatherFlag />,
			match1,
			match2Icon = <FeatherAward />,
			match2,
			className,
			...otherProps
		}: TeamComponentRootProps,
		ref,
	) {
		return (
			<div
				className={SubframeUtils.twClassNames(
					'flex w-full items-start gap-2 rounded-md border border-solid border-brand-100 bg-default-background px-4 py-4 shadow-md',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				<div className="flex grow shrink-0 basis-0 flex-col items-start gap-2">
					{teamName ? (
						<span className="text-heading-3 font-heading-3 text-default-font">{teamName}</span>
					) : null}
					<div className="flex w-full items-center gap-2">
						<Badge variant="neutral">Iron Titans CA</Badge>
						<Badge>17OR3M1</Badge>
					</div>
				</div>
				<div className="flex flex-col items-end gap-2">
					<div className="flex flex-col items-end justify-center gap-2">
						<div className="flex items-center gap-1">
							{match1Icon ? (
								<SubframeCore.IconWrapper className="text-caption font-caption text-subtext-color">
									{match1Icon}
								</SubframeCore.IconWrapper>
							) : null}
							{match1 ? (
								<span className="text-caption font-caption text-subtext-color">{match1}</span>
							) : null}
						</div>
						<div className="flex h-px w-full flex-none flex-col items-center gap-2 bg-neutral-border" />
						<div className="flex items-center gap-1">
							{match2Icon ? (
								<SubframeCore.IconWrapper className="text-caption font-caption text-subtext-color">
									{match2Icon}
								</SubframeCore.IconWrapper>
							) : null}
							{match2 ? (
								<span className="text-caption font-caption text-subtext-color">{match2}</span>
							) : null}
						</div>
					</div>
				</div>
			</div>
		);
	},
);

export const TeamComponent = TeamComponentRoot;
