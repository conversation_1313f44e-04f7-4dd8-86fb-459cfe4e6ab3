'use client';
/*
 * Documentation:
 * genralTabItem — https://app.subframe.com/5ed7fb16a283/library?component=genralTabItem_485299d9-515f-4ff9-b345-8b96d0b3207d
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';
import { FeatherWorkflow } from '@subframe/core';

interface GenralTabItemRootProps extends React.HTMLAttributes<HTMLDivElement> {
	selection?: boolean;
	icon?: React.ReactNode;
	label?: React.ReactNode;
	className?: string;
}

const GenralTabItemRoot = React.forwardRef<HTMLElement, GenralTabItemRootProps>(
	function GenralTabItemRoot(
		{
			selection = false,
			icon = <FeatherWorkflow />,
			label,
			className,
			...otherProps
		}: GenralTabItemRootProps,
		ref,
	) {
		return (
			<div
				className={SubframeUtils.twClassNames(
					'group/485299d9 flex w-full items-center justify-center gap-2 rounded-sm px-2 py-2',
					{ 'bg-brand-primary': selection },
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{icon ? (
					<SubframeCore.IconWrapper
						className={SubframeUtils.twClassNames(
							'text-heading-3 font-heading-3 text-brand-primary',
							{ 'text-white': selection },
						)}
					>
						{icon}
					</SubframeCore.IconWrapper>
				) : null}
				{label ? (
					<span
						className={SubframeUtils.twClassNames(
							'text-body-bold font-body-bold text-brand-primary',
							{ 'text-body-bold font-body-bold text-white': selection },
						)}
					>
						{label}
					</span>
				) : null}
			</div>
		);
	},
);

export const GenralTabItem = GenralTabItemRoot;
