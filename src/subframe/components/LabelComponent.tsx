'use client';
/*
 * Documentation:
 * labelComponent — https://app.subframe.com/5ed7fb16a283/library?component=labelComponent_8e411c85-7105-4962-a0dc-1acf51ec70d1
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface LabelComponentRootProps extends React.HTMLAttributes<HTMLDivElement> {
	label?: React.ReactNode;
	labelDescription?: React.ReactNode;
	className?: string;
}

const LabelComponentRoot = React.forwardRef<HTMLElement, LabelComponentRootProps>(
	function LabelComponentRoot(
		{ label, labelDescription, className, ...otherProps }: LabelComponentRootProps,
		ref,
	) {
		return (
			<div
				className={SubframeUtils.twClassNames('flex flex-col items-start gap-1', className)}
				ref={ref as any}
				{...otherProps}
			>
				{label ? (
					<span className="text-heading-3 font-heading-3 text-default-font">{label}</span>
				) : null}
				{labelDescription ? (
					<span className="text-body font-body text-default-font">{labelDescription}</span>
				) : null}
			</div>
		);
	},
);

export const LabelComponent = LabelComponentRoot;
