'use client';
/*
 * Documentation:
 * poolSwitcher — https://app.subframe.com/5ed7fb16a283/library?component=poolSwitcher_f87cee45-f123-459a-932f-acbdff71d6ba
 * buttonSquare — https://app.subframe.com/5ed7fb16a283/library?component=buttonSquare_45fe0312-4e66-46ba-8ae0-14be16ac2e01
 * Icon <PERSON> — https://app.subframe.com/5ed7fb16a283/library?component=Icon+Button_af9405b1-8c54-4e01-9786-5aad308224f6
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface PoolSwitcherRootProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
	className?: string;
}

const PoolSwitcherRoot = React.forwardRef<HTMLElement, PoolSwitcherRootProps>(
	function PoolSwitcherRoot({ children, className, ...otherProps }: PoolSwitcherRootProps, ref) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex w-80 items-center justify-between rounded-md border border-solid border-neutral-border bg-default-background px-2 py-4',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const PoolSwitcher = PoolSwitcherRoot;
