'use client';
/*
 * Documentation:
 * generalTabs — https://app.subframe.com/5ed7fb16a283/library?component=generalTabs_4a9dde1d-e1ab-4006-aac6-adbeb09d4602
 * genralTabItem — https://app.subframe.com/5ed7fb16a283/library?component=genralTabItem_485299d9-515f-4ff9-b345-8b96d0b3207d
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import { GenralTabItem } from './GenralTabItem';
import { FeatherWorkflow } from '@subframe/core';
import { FeatherUsers } from '@subframe/core';
import { FeatherMedal } from '@subframe/core';
import { FeatherSpline } from '@subframe/core';

interface GeneralTabsRootProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
}

const GeneralTabsRoot = React.forwardRef<HTMLElement, GeneralTabsRootProps>(
	function GeneralTabsRoot({ className, ...otherProps }: GeneralTabsRootProps, ref) {
		return (
			<div
				className={SubframeUtils.twClassNames(
					'flex w-80 items-center justify-center gap-1 rounded-md border border-solid border-brand-100 bg-default-background px-2 py-1.5',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				<GenralTabItem selection={true} icon={<FeatherWorkflow />} label="Pools/Brackets" />
				<GenralTabItem icon={<FeatherUsers />} label="" />
				<GenralTabItem icon={<FeatherMedal />} label="" />
				<GenralTabItem icon={<FeatherSpline />} label="" />
			</div>
		);
	},
);

export const GeneralTabs = GeneralTabsRoot;
