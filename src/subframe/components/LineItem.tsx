'use client';
/*
 * Documentation:
 * lineItem — https://app.subframe.com/5ed7fb16a283/library?component=lineItem_0282e287-9794-4909-a209-30e7e92a3021
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface LineItemRootProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'color'> {
	place?: React.ReactNode;
	points?: React.ReactNode;
	teamName?: React.ReactNode;
	matchWl?: React.ReactNode;
	setWl?: React.ReactNode;
	point?: React.ReactNode;
	code?: React.ReactNode;
	color?: 'default' | 'blue';
	children?: React.ReactNode;
	className?: string;
}

const LineItemRoot = React.forwardRef<HTMLElement, LineItemRootProps>(function LineItemRoot(
	{
		place,
		points,
		teamName,
		matchWl,
		setWl,
		point,
		code,
		color = 'default',
		children,
		className,
		...otherProps
	}: LineItemRootProps,
	ref,
) {
	return children ? (
		<div
			className={SubframeUtils.twClassNames(
				'group/0282e287 flex w-full items-center bg-default-background px-6 py-3',
				{ 'bg-brand-50': color === 'blue' },
				className,
			)}
			ref={ref as any}
			{...otherProps}
		>
			{children}
		</div>
	) : null;
});

export const LineItem = LineItemRoot;
