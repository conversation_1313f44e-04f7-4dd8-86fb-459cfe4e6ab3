'use client';
/*
 * Documentation:
 * teamHeader — https://app.subframe.com/5ed7fb16a283/library?component=teamHeader_ef5b9b48-fce8-439e-b605-73ace85754f9
 * teamTitle — https://app.subframe.com/5ed7fb16a283/library?component=teamTitle_2aed4ac8-0c49-4516-bcfe-41228bd54c35
 * matchesHorizontal — https://app.subframe.com/5ed7fb16a283/library?component=matchesHorizontal_db86f329-fcda-4ed1-a737-b96e9f3303d8
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface TeamHeaderRootProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
	className?: string;
}

const TeamHeaderRoot = React.forwardRef<HTMLElement, TeamHeaderRootProps>(function TeamHeaderRoot(
	{ children, className, ...otherProps }: TeamHeaderRootProps,
	ref,
) {
	return children ? (
		<div
			className={SubframeUtils.twClassNames(
				'flex w-full flex-col items-start gap-2 rounded-t-lg border-b border-solid border-neutral-border bg-default-background px-4 pt-4 pb-3',
				className,
			)}
			ref={ref as any}
			{...otherProps}
		>
			{children}
		</div>
	) : null;
});

export const TeamHeader = TeamHeaderRoot;
