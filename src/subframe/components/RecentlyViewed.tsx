'use client';
/*
 * Documentation:
 * recentlyViewed — https://app.subframe.com/5ed7fb16a283/library?component=recentlyViewed_7a4b8946-dc29-4836-927d-de30152d66cc
 * Link <PERSON>ton — https://app.subframe.com/5ed7fb16a283/library?component=Link+Button_a4ee726a-774c-4091-8c49-55b659356024
 * But<PERSON> — https://app.subframe.com/5ed7fb16a283/library?component=Button_3b777358-b86b-40af-9327-891efc6826fe
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import { LinkButton } from './LinkButton';
import { Button } from './Button';

interface RecentlyViewedRootProps extends Omit<React.HTMLAttributes<HTMLDivElement>, 'title'> {
	title?: React.ReactNode;
	poolsBracketsList?: React.ReactNode;
	teamsList?: React.ReactNode;
	className?: string;
}

const RecentlyViewedRoot = React.forwardRef<HTMLElement, RecentlyViewedRootProps>(
	function RecentlyViewedRoot(
		{ title, poolsBracketsList, teamsList, className, ...otherProps }: RecentlyViewedRootProps,
		ref,
	) {
		return (
			<div
				className={SubframeUtils.twClassNames(
					'flex w-80 flex-col items-start gap-2 rounded-md border border-solid border-brand-100 bg-default-background px-4 py-4 shadow-md',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				<div className="flex w-full items-center justify-between">
					{title ? (
						<span className="text-heading-2 font-heading-2 text-default-font">{title}</span>
					) : null}
					<LinkButton variant="brand">Hide</LinkButton>
				</div>
				<div className="flex h-px w-full flex-none flex-col items-center gap-2 bg-neutral-border" />
				<div className="flex w-full flex-col items-start gap-4">
					<div className="flex w-full flex-col items-start gap-2 rounded-sm border border-solid border-neutral-border bg-brand-50 px-2 py-2">
						{poolsBracketsList ? (
							<span className="text-body-bold font-body-bold text-default-font position-static">
								{poolsBracketsList}
							</span>
						) : null}
						<div className="flex w-full flex-col items-start justify-center gap-2 overflow-hidden">
							<div className="flex w-96 flex-wrap items-start gap-2 overflow-hidden">
								<Button variant="neutral-secondary" size="small">
									17A R1P7
								</Button>
								<Button variant="neutral-secondary" size="small">
									17A R3G
								</Button>
								<Button variant="neutral-secondary" size="small">
									17A R1P7
								</Button>
								<Button variant="neutral-secondary" size="small">
									17A R1P7
								</Button>
								<Button variant="neutral-secondary" size="small">
									17A R1P7
								</Button>
							</div>
						</div>
					</div>
					<div className="flex w-full flex-col items-start gap-2 rounded-sm border border-solid border-neutral-border bg-brand-50 px-2 py-2">
						{teamsList ? (
							<span className="text-body-bold font-body-bold text-default-font">{teamsList}</span>
						) : null}
						<div className="flex flex-wrap items-start gap-2">
							<Button variant="neutral-secondary" size="small">
								CJV 17 National Navy
							</Button>
							<Button variant="neutral-secondary" size="small">
								CJV 18 American
							</Button>
						</div>
					</div>
				</div>
			</div>
		);
	},
);

export const RecentlyViewed = RecentlyViewedRoot;
