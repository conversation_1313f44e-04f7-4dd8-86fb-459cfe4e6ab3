'use client';
/*
 * Documentation:
 * Area Chart — https://app.subframe.com/5ed7fb16a283/library?component=Area+Chart_8aa1e7b3-5db6-4a62-aa49-137ced21a231
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';

interface AreaChartRootProps extends React.ComponentProps<typeof SubframeCore.AreaChart> {
	stacked?: boolean;
	className?: string;
}

const AreaChartRoot = React.forwardRef<HTMLElement, AreaChartRootProps>(function AreaChartRoot(
	{ stacked = false, className, ...otherProps }: AreaChartRootProps,
	ref,
) {
	return (
		<SubframeCore.AreaChart
			className={SubframeUtils.twClassNames('h-80 w-full', className)}
			ref={ref as any}
			stacked={stacked}
			colors={['#6366f1', '#c7d2fe', '#4f46e5', '#a5b4fc', '#4338ca', '#818cf8']}
			{...otherProps}
		/>
	);
});

export const AreaChart = AreaChartRoot;
