'use client';
/*
 * Documentation:
 * tab — https://app.subframe.com/5ed7fb16a283/library?component=tab_981899b7-6a74-490b-ab82-88bad4eafca0
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface TabRootProps extends React.HTMLAttributes<HTMLDivElement> {
	variant?: 'default';
	className?: string;
}

const TabRoot = React.forwardRef<HTMLElement, TabRootProps>(function TabRoot(
	{ variant = 'default', className, ...otherProps }: TabRootProps,
	ref,
) {
	return (
		<div
			className={SubframeUtils.twClassNames(
				'group/981899b7 flex h-7 w-full cursor-pointer flex-col items-center justify-center gap-2 border-r border-solid border-neutral-border px-3 py-1.5 hover:rounded-sm hover:border-none hover:bg-brand-50 active:rounded-sm active:border-none active:bg-brand-100',
				className,
			)}
			ref={ref as any}
			{...otherProps}
		>
			<span className="text-body font-body text-subtext-color group-hover/981899b7:text-default-font group-active/981899b7:text-default-font">
				Label
			</span>
		</div>
	);
});

export const Tab = TabRoot;
