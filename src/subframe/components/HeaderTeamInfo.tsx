'use client';
/*
 * Documentation:
 * headerTeamInfo — https://app.subframe.com/5ed7fb16a283/library?component=headerTeamInfo_1be3ea64-74f2-4137-a76f-aac8a4f5c8d7
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';
import { FeatherCalendar } from '@subframe/core';

interface HeaderTeamInfoRootProps extends React.HTMLAttributes<HTMLDivElement> {
	headerIcon?: React.ReactNode;
	tableName?: React.ReactNode;
	matchScore?: React.ReactNode;
	setScore?: React.ReactNode;
	pointPercantage?: React.ReactNode;
	variant?: 'default' | 'schedule' | 'standings';
	className?: string;
}

const HeaderTeamInfoRoot = React.forwardRef<HTMLElement, HeaderTeamInfoRootProps>(
	function HeaderTeamInfoRoot(
		{
			headerIcon = <FeatherCalendar />,
			tableName,
			matchScore,
			setScore,
			pointPercantage,
			variant = 'default',
			className,
			...otherProps
		}: HeaderTeamInfoRootProps,
		ref,
	) {
		return (
			<div
				className={SubframeUtils.twClassNames(
					'group/1be3ea64 flex w-full items-center justify-between border-b border-solid border-neutral-border bg-brand-600 px-4 py-4',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				<div className="flex grow shrink-0 basis-0 items-center gap-4">
					{headerIcon ? (
						<SubframeCore.IconWrapper className="text-heading-2 font-heading-2 text-white">
							{headerIcon}
						</SubframeCore.IconWrapper>
					) : null}
					{tableName ? (
						<span className="text-heading-3 font-heading-3 text-white">{tableName}</span>
					) : null}
				</div>
				<div
					className={SubframeUtils.twClassNames('flex items-center gap-2', {
						hidden: variant === 'schedule',
					})}
				>
					{matchScore ? (
						<span className="w-20 flex-none text-body font-body text-white text-center">
							{matchScore}
						</span>
					) : null}
					{setScore ? (
						<span className="w-20 flex-none text-body font-body text-white text-center">
							{setScore}
						</span>
					) : null}
					{pointPercantage ? (
						<span className="w-16 flex-none text-body font-body text-white text-right">
							{pointPercantage}
						</span>
					) : null}
				</div>
			</div>
		);
	},
);

export const HeaderTeamInfo = HeaderTeamInfoRoot;
