'use client';
/*
 * Documentation:
 * standings Table — https://app.subframe.com/5ed7fb16a283/library?component=standings+Table_9e7a590e-86a6-4480-af02-38e861a4c0e3
 * lineItem — https://app.subframe.com/5ed7fb16a283/library?component=lineItem_0282e287-9794-4909-a209-30e7e92a3021
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface StandingsTableRootProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
	className?: string;
}

const StandingsTableRoot = React.forwardRef<HTMLElement, StandingsTableRootProps>(
	function StandingsTableRoot(
		{ children, className, ...otherProps }: StandingsTableRootProps,
		ref,
	) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex flex-col items-start overflow-hidden rounded-lg border border-solid border-neutral-border bg-default-background shadow-md',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const StandingsTable = StandingsTableRoot;
