'use client';
/*
 * Documentation:
 * teamTitle — https://app.subframe.com/5ed7fb16a283/library?component=teamTitle_2aed4ac8-0c49-4516-bcfe-41228bd54c35
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface TeamTitleRootProps extends React.HTMLAttributes<HTMLDivElement> {
	divisionName?: React.ReactNode;
	name?: React.ReactNode;
	athleteName?: React.ReactNode;
	className?: string;
}

const TeamTitleRoot = React.forwardRef<HTMLElement, TeamTitleRootProps>(function TeamTitleRoot(
	{ divisionName, name, athleteName, className, ...otherProps }: TeamTitleRootProps,
	ref,
) {
	return (
		<div
			className={SubframeUtils.twClassNames('flex w-full items-start gap-2', className)}
			ref={ref as any}
			{...otherProps}
		>
			<div className="flex items-start gap-2 py-0.5">
				<div className="flex w-10 flex-none flex-col items-center justify-center gap-2 rounded-full border border-solid border-neutral-border bg-[#3e63ddff] px-1 py-1">
					{divisionName ? (
						<span className="text-caption-bold font-caption-bold text-white">{divisionName}</span>
					) : null}
				</div>
			</div>
			<div className="flex grow shrink-0 basis-0 flex-col items-start justify-center">
				{name ? (
					<span className="w-full text-heading-2 font-heading-2 text-default-font truncate">
						{name}
					</span>
				) : null}
				{athleteName ? (
					<span className="text-body font-body text-default-font">{athleteName}</span>
				) : null}
			</div>
		</div>
	);
});

export const TeamTitle = TeamTitleRoot;
