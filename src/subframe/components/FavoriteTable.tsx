'use client';
/*
 * Documentation:
 * favoriteTable — https://app.subframe.com/5ed7fb16a283/library?component=favoriteTable_f2c7d9c8-cf96-4f3d-b4de-1b7ec1fb1d32
 * Icon Button — https://app.subframe.com/5ed7fb16a283/library?component=Icon+Button_af9405b1-8c54-4e01-9786-5aad308224f6
 * tableItem — https://app.subframe.com/5ed7fb16a283/library?component=tableItem_18282f08-a729-400e-b87f-27728e14980a
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface FavoriteTableRootProps extends React.HTMLAttributes<HTMLDivElement> {
	poolInfo?: React.ReactNode;
	children?: React.ReactNode;
	className?: string;
}

const FavoriteTableRoot = React.forwardRef<HTMLElement, FavoriteTableRootProps>(
	function FavoriteTableRoot(
		{ poolInfo, children, className, ...otherProps }: FavoriteTableRootProps,
		ref,
	) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex w-full flex-col items-start justify-between overflow-hidden rounded-md border border-solid border-brand-100',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const FavoriteTable = FavoriteTableRoot;
