'use client';
/*
 * Documentation:
 * infoCard — https://app.subframe.com/5ed7fb16a283/library?component=infoCard_ef172b07-9d28-40ab-a5b0-afb21418dd41
 * cardTitle — https://app.subframe.com/5ed7fb16a283/library?component=cardTitle_ab6dc674-de84-49f9-b019-d6f775eafd1a
 * labelComponent — https://app.subframe.com/5ed7fb16a283/library?component=labelComponent_8e411c85-7105-4962-a0dc-1acf51ec70d1
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface InfoCardRootProps extends React.HTMLAttributes<HTMLDivElement> {
	cardName?: React.ReactNode;
	children?: React.ReactNode;
	className?: string;
}

const InfoCardRoot = React.forwardRef<HTMLElement, InfoCardRootProps>(function InfoCardRoot(
	{ cardName, children, className, ...otherProps }: InfoCardRootProps,
	ref,
) {
	return children ? (
		<div
			className={SubframeUtils.twClassNames(
				'flex flex-col items-start rounded-md border border-solid border-neutral-border bg-default-background',
				className,
			)}
			ref={ref as any}
			{...otherProps}
		>
			{children}
		</div>
	) : null;
});

export const InfoCard = InfoCardRoot;
