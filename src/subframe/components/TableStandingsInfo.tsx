'use client';
/*
 * Documentation:
 * tableStandingsInfo — https://app.subframe.com/5ed7fb16a283/library?component=tableStandingsInfo_9972c4eb-fb7e-42c6-9ba9-035a7d7559ad
 * headerTeamInfo — https://app.subframe.com/5ed7fb16a283/library?component=headerTeamInfo_1be3ea64-74f2-4137-a76f-aac8a4f5c8d7
 * standingsItem — https://app.subframe.com/5ed7fb16a283/library?component=standingsItem_7a527ae7-2337-433b-8398-62043c1b2828
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import { FeatherStar } from '@subframe/core';

interface TableStandingsInfoRootProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
	className?: string;
}

const TableStandingsInfoRoot = React.forwardRef<HTMLElement, TableStandingsInfoRootProps>(
	function TableStandingsInfoRoot(
		{ children, className, ...otherProps }: TableStandingsInfoRootProps,
		ref,
	) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex w-80 flex-col items-start overflow-hidden rounded-lg border border-solid border-neutral-border shadow-md',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const TableStandingsInfo = TableStandingsInfoRoot;
