'use client';
/*
 * Documentation:
 * matchComponent — https://app.subframe.com/5ed7fb16a283/library?component=matchComponent_ccfd1df6-6ffb-45e8-b69d-614027dca1af
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import { FeatherVideo } from '@subframe/core';
import { FeatherMoreVertical } from '@subframe/core';

interface MatchComponentRootProps extends React.HTMLAttributes<HTMLDivElement> {
	matchName?: React.ReactNode;
	team1Name?: React.ReactNode;
	team1Score?: React.ReactNode;
	team2Name?: React.ReactNode;
	team2Score?: React.ReactNode;
	workingTeam?: React.ReactNode;
	children?: React.ReactNode;
	className?: string;
}

const MatchComponentRoot = React.forwardRef<HTMLElement, MatchComponentRootProps>(
	function MatchComponentRoot(
		{
			matchName,
			team1Name,
			team1Score,
			team2Name,
			team2Score,
			workingTeam,
			children,
			className,
			...otherProps
		}: MatchComponentRootProps,
		ref,
	) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex flex-col items-start gap-4 rounded-sm border border-solid border-brand-100 bg-default-background px-2.5 py-2.5 shadow-md',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const MatchComponent = MatchComponentRoot;
