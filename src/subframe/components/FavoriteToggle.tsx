'use client';
/*
 * Documentation:
 * favoriteToggle — https://app.subframe.com/5ed7fb16a283/library?component=favoriteToggle_b4a17c44-03d9-48f6-bf01-40aff4aa7301
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';
import { FeatherStar } from '@subframe/core';

interface ItemProps extends React.ComponentProps<typeof SubframeCore.ToggleGroup.Item> {
	disabled?: boolean;
	children?: React.ReactNode;
	icon?: React.ReactNode;
	className?: string;
}

const Item = React.forwardRef<HTMLElement, ItemProps>(function Item(
	{ disabled = false, children, icon = <FeatherStar />, className, ...otherProps }: ItemProps,
	ref,
) {
	return (
		<SubframeCore.ToggleGroup.Item asChild={true} {...otherProps}>
			<div
				className={SubframeUtils.twClassNames(
					'group/73855188 flex h-7 w-full cursor-pointer items-center justify-center gap-2 rounded-md px-2 py-1 active:bg-neutral-100 aria-[checked=true]:bg-default-background aria-[checked=true]:shadow-sm hover:aria-[checked=true]:bg-default-background active:aria-[checked=true]:bg-default-background',
					{ 'hover:bg-transparent active:bg-transparent': disabled },
					className,
				)}
				ref={ref as any}
			>
				{icon ? (
					<SubframeCore.IconWrapper
						className={SubframeUtils.twClassNames(
							'text-body font-body text-subtext-color group-hover/73855188:text-default-font group-active/73855188:text-default-font group-aria-[checked=true]/73855188:text-default-font',
							{
								'text-neutral-400 group-hover/73855188:text-neutral-400 group-active/73855188:text-neutral-400':
									disabled,
							},
						)}
					>
						{icon}
					</SubframeCore.IconWrapper>
				) : null}
				{children ? (
					<span
						className={SubframeUtils.twClassNames(
							'whitespace-nowrap text-caption-bold font-caption-bold text-subtext-color group-hover/73855188:text-default-font group-active/73855188:text-default-font group-aria-[checked=true]/73855188:text-default-font',
							{
								'text-neutral-400 group-hover/73855188:text-neutral-400 group-active/73855188:text-neutral-400':
									disabled,
							},
						)}
					>
						{children}
					</span>
				) : null}
			</div>
		</SubframeCore.ToggleGroup.Item>
	);
});

interface FavoriteToggleRootProps
	extends React.ComponentProps<typeof SubframeCore.ToggleGroup.Root> {
	children?: React.ReactNode;
	value?: string;
	onValueChange?: (value: string) => void;
	className?: string;
}

const FavoriteToggleRoot = React.forwardRef<HTMLElement, FavoriteToggleRootProps>(
	function FavoriteToggleRoot(
		{ children, className, ...otherProps }: FavoriteToggleRootProps,
		ref,
	) {
		return children ? (
			<SubframeCore.ToggleGroup.Root asChild={true} {...otherProps}>
				<div
					className={SubframeUtils.twClassNames(
						'flex w-full items-center gap-0.5 overflow-hidden rounded-md bg-brand-50 px-0.5 py-0.5',
						className,
					)}
					ref={ref as any}
				>
					{children}
				</div>
			</SubframeCore.ToggleGroup.Root>
		) : null;
	},
);

export const FavoriteToggle = Object.assign(FavoriteToggleRoot, {
	Item,
});
