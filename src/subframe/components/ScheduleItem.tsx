'use client';
/*
 * Documentation:
 * scheduleItem — https://app.subframe.com/5ed7fb16a283/library?component=scheduleItem_032d95ae-6962-4e46-914e-595e70688d09
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';
import { FeatherVideo } from '@subframe/core';

interface ScheduleItemRootProps extends React.HTMLAttributes<HTMLDivElement> {
	match?: React.ReactNode;
	stream?: React.ReactNode;
	team1?: React.ReactNode;
	team2?: React.ReactNode;
	work?: React.ReactNode;
	dateTime?: React.ReactNode;
	court?: React.ReactNode;
	className?: string;
}

const ScheduleItemRoot = React.forwardRef<HTMLElement, ScheduleItemRootProps>(
	function ScheduleItemRoot(
		{
			match,
			stream = <FeatherVideo />,
			team1,
			team2,
			work,
			dateTime,
			court,
			className,
			...otherProps
		}: ScheduleItemRootProps,
		ref,
	) {
		return (
			<div
				className={SubframeUtils.twClassNames('flex w-112 flex-col items-start', className)}
				ref={ref as any}
				{...otherProps}
			>
				<div className="flex w-full items-start justify-between border-b border-solid border-neutral-border px-4 py-4">
					<div className="flex grow shrink-0 basis-0 items-start gap-2 self-stretch">
						<div className="flex w-6 flex-none flex-col items-center justify-between self-stretch">
							{match ? (
								<span className="text-body font-body text-default-font">{match}</span>
							) : null}
							{stream ? (
								<SubframeCore.IconWrapper className="text-heading-3 font-heading-3 text-brand-600">
									{stream}
								</SubframeCore.IconWrapper>
							) : null}
						</div>
						<div className="flex grow shrink-0 basis-0 flex-col items-start justify-between self-stretch">
							{team1 ? (
								<span className="w-64 text-body-bold font-body-bold text-default-font truncate">
									{team1}
								</span>
							) : null}
							{team2 ? (
								<span className="w-64 text-body-bold font-body-bold text-default-font truncate">
									{team2}
								</span>
							) : null}
							{work ? (
								<span className="text-caption font-caption text-subtext-color italic">{work}</span>
							) : null}
						</div>
					</div>
					<div className="flex flex-col items-end gap-1">
						{dateTime ? (
							<span className="text-body font-body text-default-font">{dateTime}</span>
						) : null}
						{court ? (
							<span className="text-caption font-caption text-subtext-color">{court}</span>
						) : null}
					</div>
				</div>
			</div>
		);
	},
);

export const ScheduleItem = ScheduleItemRoot;
