'use client';
/*
 * Documentation:
 * matchesHorizontal — https://app.subframe.com/5ed7fb16a283/library?component=matchesHorizontal_db86f329-fcda-4ed1-a737-b96e9f3303d8
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';
import { FeatherFlag } from '@subframe/core';
import { FeatherAward } from '@subframe/core';

interface MatchesHorizontalRootProps extends React.HTMLAttributes<HTMLDivElement> {
	match1Icon?: React.ReactNode;
	match1Time?: React.ReactNode;
	match2Icon?: React.ReactNode;
	match2Time?: React.ReactNode;
	className?: string;
}

const MatchesHorizontalRoot = React.forwardRef<HTMLElement, MatchesHorizontalRootProps>(
	function MatchesHorizontalRoot(
		{
			match1Icon = <FeatherFlag />,
			match1Time,
			match2Icon = <FeatherAward />,
			match2Time,
			className,
			...otherProps
		}: MatchesHorizontalRootProps,
		ref,
	) {
		return (
			<div
				className={SubframeUtils.twClassNames('flex flex-col items-end gap-2', className)}
				ref={ref as any}
				{...otherProps}
			>
				<div className="flex items-end justify-center gap-2">
					<div className="flex items-center gap-1">
						{match1Icon ? (
							<SubframeCore.IconWrapper className="text-caption font-caption text-subtext-color">
								{match1Icon}
							</SubframeCore.IconWrapper>
						) : null}
						{match1Time ? (
							<span className="text-caption font-caption text-subtext-color">{match1Time}</span>
						) : null}
					</div>
					<div className="flex w-px flex-none flex-col items-center gap-2 self-stretch bg-neutral-border" />
					<div className="flex items-center gap-1">
						{match2Icon ? (
							<SubframeCore.IconWrapper className="text-caption font-caption text-subtext-color">
								{match2Icon}
							</SubframeCore.IconWrapper>
						) : null}
						{match2Time ? (
							<span className="text-caption font-caption text-subtext-color">{match2Time}</span>
						) : null}
					</div>
				</div>
			</div>
		);
	},
);

export const MatchesHorizontal = MatchesHorizontalRoot;
