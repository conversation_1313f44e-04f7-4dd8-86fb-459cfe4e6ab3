'use client';
/*
 * Documentation:
 * icon tab — https://app.subframe.com/5ed7fb16a283/library?component=icon+tab_fcc0c41b-e45d-4f0e-a017-33b7da874cab
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import { FeatherMoreHorizontal } from '@subframe/core';

interface IconTabRootProps extends React.HTMLAttributes<HTMLDivElement> {
	variant?: 'default';
	className?: string;
}

const IconTabRoot = React.forwardRef<HTMLElement, IconTabRootProps>(function IconTabRoot(
	{ variant = 'default', className, ...otherProps }: IconTabRootProps,
	ref,
) {
	return (
		<div
			className={SubframeUtils.twClassNames(
				'group/fcc0c41b flex h-7 cursor-pointer flex-col items-center justify-center gap-2 px-3 py-1.5 hover:rounded-sm hover:bg-neutral-50 active:rounded-sm active:bg-default-background',
				className,
			)}
			ref={ref as any}
			{...otherProps}
		>
			<FeatherMoreHorizontal className="text-body font-body text-subtext-color" />
		</div>
	);
});

export const IconTab = IconTabRoot;
