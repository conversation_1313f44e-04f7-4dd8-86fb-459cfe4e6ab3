'use client';
/*
 * Documentation:
 * countdown — https://app.subframe.com/5ed7fb16a283/library?component=countdown_dbfab425-2564-4996-852f-97a70b54d28d
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface CountdownRootProps extends React.HTMLAttributes<HTMLDivElement> {
	day?: React.ReactNode;
	hour?: React.ReactNode;
	minute?: React.ReactNode;
	className?: string;
}

const CountdownRoot = React.forwardRef<HTMLElement, CountdownRootProps>(function CountdownRoot(
	{ day, hour, minute, className, ...otherProps }: CountdownRootProps,
	ref,
) {
	return (
		<div
			className={SubframeUtils.twClassNames('flex items-center gap-2', className)}
			ref={ref as any}
			{...otherProps}
		>
			<div className="flex flex-col items-center justify-center rounded-sm">
				<div className="flex w-7 flex-col items-center justify-center rounded-sm bg-brand-600 px-1 py-1">
					{day ? (
						<span className="w-6 text-heading-3 font-heading-3 text-white text-center">{day}</span>
					) : null}
				</div>
				<span className="text-caption font-caption text-brand-primary">days</span>
			</div>
			<span className="text-heading-2 font-heading-2 text-default-font">:</span>
			<div className="flex flex-col items-center justify-center rounded-sm">
				<div className="flex w-7 flex-col items-center justify-center rounded-sm bg-brand-600 px-1 py-1">
					{hour ? (
						<span className="w-6 text-heading-3 font-heading-3 text-white text-center">{hour}</span>
					) : null}
				</div>
				<span className="text-caption font-caption text-brand-primary">hours</span>
			</div>
			<span className="text-heading-2 font-heading-2 text-default-font">:</span>
			<div className="flex flex-col items-center justify-center rounded-sm">
				<div className="flex w-7 flex-col items-center justify-center rounded-sm bg-brand-600 px-1 py-1">
					{minute ? (
						<span className="w-6 text-heading-3 font-heading-3 text-white text-center">
							{minute}
						</span>
					) : null}
				</div>
				<span className="text-caption font-caption text-brand-primary">mins.</span>
			</div>
		</div>
	);
});

export const Countdown = CountdownRoot;
