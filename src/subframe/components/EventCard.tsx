'use client';
/*
 * Documentation:
 * eventCard — https://app.subframe.com/5ed7fb16a283/library?component=eventCard_4093156a-353a-453a-a47c-e98c7d665d7a
 * Avatar — https://app.subframe.com/5ed7fb16a283/library?component=Avatar_bec25ae6-5010-4485-b46b-cf79e3943ab2
 * Icon with background — https://app.subframe.com/5ed7fb16a283/library?component=Icon+with+background_c5d68c0e-4c0c-4cff-8d8c-6ff334859b3a
 * Icon <PERSON>ton — https://app.subframe.com/5ed7fb16a283/library?component=Icon+Button_af9405b1-8c54-4e01-9786-5aad308224f6
 * buttonSquare — https://app.subframe.com/5ed7fb16a283/library?component=buttonSquare_45fe0312-4e66-46ba-8ae0-14be16ac2e01
 * Button — https://app.subframe.com/5ed7fb16a283/library?component=Button_3b777358-b86b-40af-9327-891efc6826fe
 * countdown — https://app.subframe.com/5ed7fb16a283/library?component=countdown_dbfab425-2564-4996-852f-97a70b54d28d
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import { Avatar } from './Avatar';
import { IconWithBackground } from './IconWithBackground';
import { FeatherArrowUpRight } from '@subframe/core';
import { IconButton } from './IconButton';
import { FeatherMoreVertical } from '@subframe/core';
import { ButtonSquare } from './ButtonSquare';
import { FeatherStar } from '@subframe/core';
import { Button } from './Button';
import { FeatherRefreshCcw } from '@subframe/core';
import { Countdown } from './Countdown';

interface EventCardRootProps extends React.HTMLAttributes<HTMLDivElement> {
	eventName1?: React.ReactNode;
	eventName2?: React.ReactNode;
	count?: React.ReactNode;
	dayCount?: React.ReactNode;
	labelDay?: React.ReactNode;
	sep?: React.ReactNode;
	text7?: React.ReactNode;
	text8?: React.ReactNode;
	se?: React.ReactNode;
	text10?: React.ReactNode;
	text11?: React.ReactNode;
	text12?: React.ReactNode;
	text13?: React.ReactNode;
	text14?: React.ReactNode;
	text15?: React.ReactNode;
	text16?: React.ReactNode;
	text17?: React.ReactNode;
	text18?: React.ReactNode;
	text19?: React.ReactNode;
	text20?: React.ReactNode;
	text21?: React.ReactNode;
	text22?: React.ReactNode;
	text23?: React.ReactNode;
	text24?: React.ReactNode;
	text25?: React.ReactNode;
	text26?: React.ReactNode;
	text27?: React.ReactNode;
	text28?: React.ReactNode;
	text29?: React.ReactNode;
	text30?: React.ReactNode;
	className?: string;
}

const EventCardRoot = React.forwardRef<HTMLElement, EventCardRootProps>(function EventCardRoot(
	{
		eventName1,
		eventName2,
		count,
		dayCount,
		labelDay,
		sep,
		text7,
		text8,
		se,
		text10,
		text11,
		text12,
		text13,
		text14,
		text15,
		text16,
		text17,
		text18,
		text19,
		text20,
		text21,
		text22,
		text23,
		text24,
		text25,
		text26,
		text27,
		text28,
		text29,
		text30,
		className,
		...otherProps
	}: EventCardRootProps,
	ref,
) {
	return (
		<div
			className={SubframeUtils.twClassNames(
				'flex flex-col items-start gap-6 rounded-md bg-default-background px-6 py-6 shadow-lg',
				className,
			)}
			ref={ref as any}
			{...otherProps}
		>
			<div className="flex w-full items-start justify-between">
				<div className="flex items-start gap-4">
					<Avatar image="https://res.cloudinary.com/subframe/image/upload/v1750680547/uploads/17055/a9s7ikne6vlaxemczcst.png">
						G
					</Avatar>
					<div className="flex flex-col items-start gap-1">
						{eventName1 ? (
							<span className="text-heading-2 font-heading-2 text-default-font">{eventName1}</span>
						) : null}
						<div className="flex items-center gap-2">
							{eventName2 ? (
								<span className="text-heading-2 font-heading-2 text-default-font">
									{eventName2}
								</span>
							) : null}
							<IconWithBackground icon={<FeatherArrowUpRight />} square={true} />
						</div>
					</div>
					<IconButton icon={<FeatherMoreVertical />} />
				</div>
				<div className="flex items-center gap-2">
					<ButtonSquare
						disabled={false}
						variant="favorite"
						size="medium"
						iconRight={<FeatherStar />}
						loading={false}
					>
						Star Event
					</ButtonSquare>
					<Button iconRight={<FeatherRefreshCcw />}>Additional Info</Button>
				</div>
			</div>
			<div className="flex items-start gap-6">
				<div className="flex flex-col items-start gap-4">
					<div className="flex w-96 items-start justify-between rounded-sm border border-solid border-neutral-border px-4 py-4">
						{count ? (
							<span className="w-32 flex-none text-heading-3 font-heading-3 text-default-font">
								{count}
							</span>
						) : null}
						<Countdown day="35" hour="35" minute="35" />
					</div>
					<div className="flex w-96 flex-col items-start gap-4 rounded-md bg-brand-50 px-4 py-4">
						<span className="text-heading-3 font-heading-3 text-default-font">Statistics</span>
						<div className="flex w-full flex-wrap items-start gap-2">
							<div className="flex grow shrink-0 basis-0 flex-col items-start gap-1 rounded-md bg-white px-2 py-2">
								<span className="text-caption font-caption text-subtext-color">Teams Entered</span>
								<span className="text-heading-2 font-heading-2 text-brand-600">385</span>
							</div>
							<div className="flex grow shrink-0 basis-0 flex-col items-start gap-1 rounded-md bg-white px-2 py-2">
								<span className="text-caption font-caption text-subtext-color">Total Profit</span>
								<span className="text-heading-2 font-heading-2 text-brand-600">$987,358</span>
							</div>
							<div className="flex grow shrink-0 basis-0 flex-col items-start gap-1 rounded-md bg-white px-2 py-2">
								<span className="text-caption font-caption text-subtext-color">Gross Profit</span>
								<span className="text-heading-2 font-heading-2 text-brand-600">$0</span>
							</div>
						</div>
						<div className="flex w-full flex-wrap items-start gap-2">
							<div className="flex grow shrink-0 basis-0 flex-col items-start gap-1 rounded-md bg-white px-2 py-2">
								<span className="text-caption font-caption text-subtext-color">
									Officials/Staff
								</span>
								<span className="text-heading-2 font-heading-2 text-brand-600">83/13</span>
							</div>
							<div className="flex w-20 flex-none flex-col items-start gap-1 rounded-md bg-white px-2 py-2">
								<span className="text-caption font-caption text-subtext-color">Exhibitors</span>
								<span className="text-heading-2 font-heading-2 text-brand-600">18</span>
							</div>
							<div className="flex grow shrink-0 basis-0 flex-col items-start gap-1 rounded-md bg-white px-2 py-2">
								<span className="text-caption font-caption text-subtext-color">
									Total Sent Payouts
								</span>
								<span className="text-heading-2 font-heading-2 text-brand-600">$0</span>
							</div>
						</div>
					</div>
				</div>
				<div className="flex flex-col items-start gap-4">
					<div className="flex w-96 flex-col items-start gap-4 rounded-md bg-brand-50 px-4 py-4">
						{text25 ? (
							<span className="text-heading-3 font-heading-3 text-default-font">{text25}</span>
						) : null}
						<div className="flex w-full flex-wrap items-start gap-4">
							<div className="flex grow shrink-0 basis-0 flex-col items-start gap-1 rounded-md bg-white px-2 py-2">
								{text26 ? (
									<span className="text-caption font-caption text-subtext-color">{text26}</span>
								) : null}
								{text27 ? (
									<span className="text-heading-2 font-heading-2 text-brand-600">{text27}</span>
								) : null}
							</div>
							<div className="flex grow shrink-0 basis-0 flex-col items-start gap-1 rounded-md bg-white px-2 py-2">
								{text28 ? (
									<span className="text-caption font-caption text-subtext-color">{text28}</span>
								) : null}
								{text29 ? (
									<span className="text-heading-2 font-heading-2 text-brand-600">{text29}</span>
								) : null}
							</div>
						</div>
					</div>
					<div className="flex w-96 flex-col items-start gap-2">
						{text30 ? (
							<span className="text-heading-3 font-heading-3 text-default-font">{text30}</span>
						) : null}
						<div className="flex w-full flex-wrap items-start gap-1">
							<div className="flex flex-wrap items-start gap-2">
								<Button variant="brand-secondary">Teams</Button>
								<Button variant="brand-secondary">Divisions</Button>
								<Button variant="neutral-secondary">Check In</Button>
							</div>
							<div className="flex flex-wrap items-start gap-2">
								<Button variant="neutral-secondary">Tickets</Button>
								<Button variant="brand-secondary">Officials</Button>
								<Button variant="brand-secondary">Staff</Button>
								<Button variant="brand-secondary">Exhibitors</Button>
							</div>
							<div className="flex flex-wrap items-start gap-2">
								<Button variant="brand-secondary">Accounting</Button>
								<Button variant="brand-secondary">Email Module</Button>
								<Button variant="brand-secondary">History</Button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
});

export const EventCard = EventCardRoot;
