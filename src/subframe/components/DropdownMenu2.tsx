'use client';
/*
 * Documentation:
 * Dropdown Menu2 — https://app.subframe.com/5ed7fb16a283/library?component=Dropdown+Menu2_1ee28238-7fc1-4e15-a8b6-23c07e10c16d
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';
import { FeatherStar } from '@subframe/core';

interface DropdownItemProps extends React.ComponentProps<typeof SubframeCore.DropdownMenu.Item> {
	children?: React.ReactNode;
	icon?: React.ReactNode;
	className?: string;
}

const DropdownItem = React.forwardRef<HTMLElement, DropdownItemProps>(function DropdownItem(
	{ children, icon = <FeatherStar />, className, ...otherProps }: DropdownItemProps,
	ref,
) {
	return (
		<SubframeCore.DropdownMenu.Item asChild={true} {...otherProps}>
			<div
				className={SubframeUtils.twClassNames(
					'group/e50471c1 flex h-8 w-full cursor-pointer items-center gap-2 rounded-md px-3 hover:bg-neutral-100 active:bg-neutral-50 data-[highlighted]:bg-neutral-100',
					className,
				)}
				ref={ref as any}
			>
				{icon ? (
					<SubframeCore.IconWrapper className="text-body font-body text-default-font">
						{icon}
					</SubframeCore.IconWrapper>
				) : null}
				{children ? (
					<span className="line-clamp-1 grow shrink-0 basis-0 text-caption font-caption text-default-font group-hover/e50471c1:text-default-font">
						{children}
					</span>
				) : null}
			</div>
		</SubframeCore.DropdownMenu.Item>
	);
});

interface DropdownDividerProps extends React.HTMLAttributes<HTMLDivElement> {
	className?: string;
}

const DropdownDivider = React.forwardRef<HTMLElement, DropdownDividerProps>(
	function DropdownDivider({ className, ...otherProps }: DropdownDividerProps, ref) {
		return (
			<div
				className={SubframeUtils.twClassNames('flex w-full items-start gap-2 px-1 py-1', className)}
				ref={ref as any}
				{...otherProps}
			>
				<div className="flex h-px grow shrink-0 basis-0 flex-col items-center gap-2 bg-neutral-200" />
			</div>
		);
	},
);

interface DropdownMenu2RootProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
	className?: string;
}

const DropdownMenu2Root = React.forwardRef<HTMLElement, DropdownMenu2RootProps>(
	function DropdownMenu2Root({ children, className, ...otherProps }: DropdownMenu2RootProps, ref) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex min-w-[192px] flex-col items-start rounded-md border border-solid border-neutral-border bg-default-background px-1 py-1 shadow-lg',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const DropdownMenu2 = Object.assign(DropdownMenu2Root, {
	DropdownItem,
	DropdownDivider,
});
