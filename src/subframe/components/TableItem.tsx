'use client';
/*
 * Documentation:
 * tableItem — https://app.subframe.com/5ed7fb16a283/library?component=tableItem_18282f08-a729-400e-b87f-27728e14980a
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface TableItemRootProps extends React.HTMLAttributes<HTMLDivElement> {
	variant?: 'default' | 'blue';
	divisionName?: React.ReactNode;
	roundName?: React.ReactNode;
	poolName?: React.ReactNode;
	description?: React.ReactNode;
	time?: React.ReactNode;
	courtName?: React.ReactNode;
	className?: string;
}

const TableItemRoot = React.forwardRef<HTMLElement, TableItemRootProps>(function TableItemRoot(
	{
		variant = 'default',
		divisionName,
		roundName,
		poolName,
		description,
		time,
		courtName,
		className,
		...otherProps
	}: TableItemRootProps,
	ref,
) {
	return (
		<div
			className={SubframeUtils.twClassNames(
				'group/18282f08 flex h-12 w-full items-center justify-between border-t border-solid border-neutral-border bg-default-background px-2.5 py-4',
				{ 'bg-brand-50': variant === 'blue' },
				className,
			)}
			ref={ref as any}
			{...otherProps}
		>
			<div className="flex items-center justify-between">
				{divisionName ? (
					<span className="text-body font-body text-default-font">{divisionName}</span>
				) : null}
				{roundName ? (
					<span className="text-body font-body text-default-font">{roundName}</span>
				) : null}
				{poolName ? (
					<span className="text-body font-body text-default-font">{poolName}</span>
				) : null}
			</div>
			{description ? (
				<span className="text-body font-body text-default-font">{description}</span>
			) : null}
			<div className="flex items-center justify-end gap-4 px-1 py-1">
				{time ? <span className="text-body font-body text-default-font">{time}</span> : null}
				{courtName ? (
					<span className="text-body font-body text-default-font italic">{courtName}</span>
				) : null}
			</div>
		</div>
	);
});

export const TableItem = TableItemRoot;
