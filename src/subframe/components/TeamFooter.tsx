'use client';
/*
 * Documentation:
 * teamFooter — https://app.subframe.com/5ed7fb16a283/library?component=teamFooter_634cba28-1635-4fa1-8f5a-d395abe08bd7
 * favoriteToggle — https://app.subframe.com/5ed7fb16a283/library?component=favoriteToggle_b4a17c44-03d9-48f6-bf01-40aff4aa7301
 * Icon <PERSON>ton — https://app.subframe.com/5ed7fb16a283/library?component=Icon+Button_af9405b1-8c54-4e01-9786-5aad308224f6
 * tableItem — https://app.subframe.com/5ed7fb16a283/library?component=tableItem_18282f08-a729-400e-b87f-27728e14980a
 * favoriteTable — https://app.subframe.com/5ed7fb16a283/library?component=favoriteTable_f2c7d9c8-cf96-4f3d-b4de-1b7ec1fb1d32
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface TeamFooterRootProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
	className?: string;
}

const TeamFooterRoot = React.forwardRef<HTMLElement, TeamFooterRootProps>(function TeamFooterRoot(
	{ children, className, ...otherProps }: TeamFooterRootProps,
	ref,
) {
	return children ? (
		<div
			className={SubframeUtils.twClassNames(
				'flex flex-col items-start gap-2 rounded-b-lg bg-default-background px-4 py-4',
				className,
			)}
			ref={ref as any}
			{...otherProps}
		>
			{children}
		</div>
	) : null;
});

export const TeamFooter = TeamFooterRoot;
