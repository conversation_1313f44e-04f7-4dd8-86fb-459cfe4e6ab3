'use client';
/*
 * Documentation:
 * favoriteFull — https://app.subframe.com/5ed7fb16a283/library?component=favoriteFull_a9885b91-ed2a-4ec7-ac7a-c3769e6b3229
 * teamTitle — https://app.subframe.com/5ed7fb16a283/library?component=teamTitle_2aed4ac8-0c49-4516-bcfe-41228bd54c35
 * matchesHorizontal — https://app.subframe.com/5ed7fb16a283/library?component=matchesHorizontal_db86f329-fcda-4ed1-a737-b96e9f3303d8
 * teamHeader — https://app.subframe.com/5ed7fb16a283/library?component=teamHeader_ef5b9b48-fce8-439e-b605-73ace85754f9
 * favoriteToggle — https://app.subframe.com/5ed7fb16a283/library?component=favoriteToggle_b4a17c44-03d9-48f6-bf01-40aff4aa7301
 * Dropdown Menu — https://app.subframe.com/5ed7fb16a283/library?component=Dropdown+Menu_99951515-459b-4286-919e-a89e7549b43b
 * Icon Button — https://app.subframe.com/5ed7fb16a283/library?component=Icon+Button_af9405b1-8c54-4e01-9786-5aad308224f6
 * tableItem — https://app.subframe.com/5ed7fb16a283/library?component=tableItem_18282f08-a729-400e-b87f-27728e14980a
 * favoriteTable — https://app.subframe.com/5ed7fb16a283/library?component=favoriteTable_f2c7d9c8-cf96-4f3d-b4de-1b7ec1fb1d32
 * teamFooter — https://app.subframe.com/5ed7fb16a283/library?component=teamFooter_634cba28-1635-4fa1-8f5a-d395abe08bd7
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import { DropdownMenu } from './DropdownMenu';
import { FeatherStar } from '@subframe/core';
import { FeatherPlus } from '@subframe/core';
import { FeatherEdit2 } from '@subframe/core';
import { FeatherTrash } from '@subframe/core';
import * as SubframeCore from '@subframe/core';

interface FavoriteFullRootProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
	className?: string;
}

const FavoriteFullRoot = React.forwardRef<HTMLElement, FavoriteFullRootProps>(
	function FavoriteFullRoot({ children, className, ...otherProps }: FavoriteFullRootProps, ref) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex w-96 flex-col items-start justify-end rounded-lg border border-solid border-brand-100 shadow-md',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const FavoriteFull = FavoriteFullRoot;
