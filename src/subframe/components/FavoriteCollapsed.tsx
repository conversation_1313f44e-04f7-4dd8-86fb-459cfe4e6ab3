'use client';
/*
 * Documentation:
 * favoriteCollapsed — https://app.subframe.com/5ed7fb16a283/library?component=favoriteCollapsed_cfec09f6-ff24-40db-944f-0cbf33e7e00a
 * teamTitle — https://app.subframe.com/5ed7fb16a283/library?component=teamTitle_2aed4ac8-0c49-4516-bcfe-41228bd54c35
 * matchesHorizontal — https://app.subframe.com/5ed7fb16a283/library?component=matchesHorizontal_db86f329-fcda-4ed1-a737-b96e9f3303d8
 * teamHeader — https://app.subframe.com/5ed7fb16a283/library?component=teamHeader_ef5b9b48-fce8-439e-b605-73ace85754f9
 * favoriteToggle — https://app.subframe.com/5ed7fb16a283/library?component=favoriteToggle_b4a17c44-03d9-48f6-bf01-40aff4aa7301
 * Dropdown Menu — https://app.subframe.com/5ed7fb16a283/library?component=Dropdown+Menu_99951515-459b-4286-919e-a89e7549b43b
 * teamFooter — https://app.subframe.com/5ed7fb16a283/library?component=teamFooter_634cba28-1635-4fa1-8f5a-d395abe08bd7
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import { DropdownMenu } from './DropdownMenu';
import { FeatherStar } from '@subframe/core';
import { FeatherPlus } from '@subframe/core';
import { FeatherEdit2 } from '@subframe/core';
import { FeatherTrash } from '@subframe/core';
import * as SubframeCore from '@subframe/core';

interface FavoriteCollapsedRootProps extends React.HTMLAttributes<HTMLDivElement> {
	children?: React.ReactNode;
	athleteName?: React.ReactNode;
	name?: React.ReactNode;
	divisionName?: React.ReactNode;
	className?: string;
}

const FavoriteCollapsedRoot = React.forwardRef<HTMLElement, FavoriteCollapsedRootProps>(
	function FavoriteCollapsedRoot(
		{
			children,
			athleteName,
			name,
			divisionName,
			className,
			...otherProps
		}: FavoriteCollapsedRootProps,
		ref,
	) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex w-96 flex-col items-start justify-end rounded-lg border border-solid border-brand-100 shadow-md',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const FavoriteCollapsed = FavoriteCollapsedRoot;
