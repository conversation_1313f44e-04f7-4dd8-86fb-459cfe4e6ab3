'use client';
/*
 * Documentation:
 * tableScheduleInfo — https://app.subframe.com/5ed7fb16a283/library?component=tableScheduleInfo_17a015ce-3b8b-4971-bbea-2218c8fc82f2
 * headerTeamInfo — https://app.subframe.com/5ed7fb16a283/library?component=headerTeamInfo_1be3ea64-74f2-4137-a76f-aac8a4f5c8d7
 * scheduleItem — https://app.subframe.com/5ed7fb16a283/library?component=scheduleItem_032d95ae-6962-4e46-914e-595e70688d09
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface TableScheduleInfoRootProps extends React.HTMLAttributes<HTMLDivElement> {
	variant?: 'default';
	children?: React.ReactNode;
	className?: string;
}

const TableScheduleInfoRoot = React.forwardRef<HTMLElement, TableScheduleInfoRootProps>(
	function TableScheduleInfoRoot(
		{ variant = 'default', children, className, ...otherProps }: TableScheduleInfoRootProps,
		ref,
	) {
		return children ? (
			<div
				className={SubframeUtils.twClassNames(
					'flex flex-col items-start overflow-hidden rounded-lg border border-solid border-neutral-border shadow-md',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				{children}
			</div>
		) : null;
	},
);

export const TableScheduleInfo = TableScheduleInfoRoot;
