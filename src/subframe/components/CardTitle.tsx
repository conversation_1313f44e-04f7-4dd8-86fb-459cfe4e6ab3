'use client';
/*
 * Documentation:
 * cardTitle — https://app.subframe.com/5ed7fb16a283/library?component=cardTitle_ab6dc674-de84-49f9-b019-d6f775eafd1a
 */

import React from 'react';
import * as SubframeUtils from '../utils';

interface CardTitleRootProps extends React.HTMLAttributes<HTMLDivElement> {
	cardName?: React.ReactNode;
	children?: React.ReactNode;
	className?: string;
}

const CardTitleRoot = React.forwardRef<HTMLElement, CardTitleRootProps>(function CardTitleRoot(
	{ cardName, children, className, ...otherProps }: CardTitleRootProps,
	ref,
) {
	return children ? (
		<div
			className={SubframeUtils.twClassNames(
				'flex w-full flex-col items-start gap-2 border-b border-solid border-neutral-border',
				className,
			)}
			ref={ref as any}
			{...otherProps}
		>
			{children}
		</div>
	) : null;
});

export const CardTitle = CardTitleRoot;
