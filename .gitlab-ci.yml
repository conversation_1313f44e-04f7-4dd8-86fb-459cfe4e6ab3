# Cache modules using lock file
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/

stages:
  - install
  - build
  - deploy
  - publish

variables:
  NODE_IMAGE: "node:22"

# Install dependencies once
install_dependencies:
  stage: install
  script:
    - docker run --rm -v `pwd`:/build -w /build -u $(id -u):$(id -g) --env HOME=/build --env NPM_CONFIG_CACHE=/build/.npm ${NODE_IMAGE} bash -c 'mkdir -p /build/.npm && npm ci'
  artifacts:
    paths:
      - node_modules/
    expire_in: 15 minutes
  only:
    - master
    - development

# Build both library package and Storybook
build:
  stage: build
  dependencies:
    - install_dependencies
  script:
    - docker run --rm -v `pwd`:/build -w /build -u $(id -u):$(id -g) --env HOME=/build ${NODE_IMAGE} bash -c 'npm run build && npm run build-storybook'
  artifacts:
    paths:
      - dist/
      - storybook-static/
    expire_in: 5 minutes
  only:
    - master
    - development

# Deploy Storybook static files (existing functionality)
deploy_static:
  stage: deploy
  dependencies:
    - build
  script:
    - ansible-playbook -l marc-do-dev-sw-sw deploy/static.yml
    - ansible-playbook -l marc-aws-sw-dev deploy/static.yml
  only:
    - master
    - development

# Publish package to GitLab registry
publish_package:
  stage: publish
  dependencies:
    - build
  script:
    - |
      docker run --rm -v `pwd`:/build -w /build -u $(id -u):$(id -g) \
        --env HOME=/build \
        --env CI_JOB_TOKEN=${CI_JOB_TOKEN} \
        --env CI_SERVER_HOST=${CI_SERVER_HOST} \
        --env CI_PROJECT_ID=${CI_PROJECT_ID} \
        ${NODE_IMAGE} \
        bash -c '
          # Create .npmrc for GitLab registry authentication
          echo "registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/" > .npmrc
          echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc

          echo "Publishing package..."
          npm publish
          echo "Package published successfully!"
        '
  only:
    - master
