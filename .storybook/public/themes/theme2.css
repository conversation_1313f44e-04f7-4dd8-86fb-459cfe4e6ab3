/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@import url('https://fonts.googleapis.com/css2?family=Public+Sans:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@100;200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@layer properties;
@layer theme, base, components, utilities;
@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
      "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
      "Courier New", monospace;
    --color-neutral-50: rgb(35, 30, 40);
    --color-neutral-100: rgb(50, 42, 55);
    --color-neutral-200: rgb(75, 62, 80);
    --color-neutral-300: rgb(105, 88, 115);
    --color-neutral-400: rgb(140, 118, 155);
    --color-neutral-600: rgb(195, 168, 205);
    --color-neutral-700: rgb(215, 188, 225);
    --color-neutral-800: rgb(230, 208, 240);
    --color-neutral-900: rgb(240, 225, 250);
    --color-white: rgb(20, 18, 25);
    --spacing: 0.25rem;
    --tracking-normal: 0em;
    --radius-sm: 8px;
    --radius-md: 16px;
    --radius-lg: 24px;
    --radius-xl: 0.75rem;
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
    --color-brand-50: rgb(255, 238, 205);
    --color-brand-100: rgb(255, 220, 155);
    --color-brand-200: rgb(255, 185, 85);
    --color-brand-500: rgb(220, 95, 0);
    --color-brand-600: rgb(185, 75, 15);
    --color-brand-700: rgb(155, 60, 35);
    --color-brand-800: rgb(125, 45, 55);
    --color-brand-900: rgb(95, 35, 75);
    --color-error-50: rgb(240, 255, 235);
    --color-error-100: rgb(215, 255, 205);
    --color-error-500: rgb(45, 245, 35);
    --color-error-600: rgb(25, 215, 15);
    --color-error-700: rgb(15, 185, 5);
    --color-error-800: rgb(10, 155, 0);
    --color-error-900: rgb(5, 125, 0);
    --color-warning-50: rgb(245, 235, 255);
    --color-warning-100: rgb(225, 205, 255);
    --color-warning-500: rgb(85, 25, 245);
    --color-warning-800: rgb(55, 8, 155);
    --color-warning-900: rgb(45, 5, 125);
    --color-success-50: rgb(255, 245, 240);
    --color-success-100: rgb(255, 225, 215);
    --color-success-700: rgb(185, 35, 0);
    --color-success-800: rgb(155, 25, 0);
    --color-success-900: rgb(125, 15, 0);
    --color-brand-primary: rgb(185, 75, 15);
    --color-default-font: rgb(248, 238, 255);
    --color-subtext-color: rgb(175, 148, 185);
    --color-neutral-border: rgb(75, 62, 80);
    --color-default-background: rgb(20, 18, 25);
    --text-caption: 12px;
    --text-caption--font-weight: 400;
    --text-caption--letter-spacing: 0em;
    --text-caption--line-height: 16px;
    --text-caption-bold: 12px;
    --text-caption-bold--font-weight: 600;
    --text-caption-bold--letter-spacing: 0em;
    --text-caption-bold--line-height: 16px;
    --text-body: 14px;
    --text-body--font-weight: 400;
    --text-body--letter-spacing: 0em;
    --text-body--line-height: 20px;
    --text-body-bold: 14px;
    --text-body-bold--font-weight: 600;
    --text-body-bold--letter-spacing: 0em;
    --text-body-bold--line-height: 20px;
    --text-heading-3: 16px;
    --text-heading-3--font-weight: 700;
    --text-heading-3--letter-spacing: 0em;
    --text-heading-3--line-height: 20px;
    --text-heading-2: 20px;
    --text-heading-2--font-weight: 700;
    --text-heading-2--letter-spacing: 0em;
    --text-heading-2--line-height: 24px;
    --text-heading-1: 30px;
    --text-heading-1--font-weight: 700;
    --text-heading-1--letter-spacing: 0em;
    --text-heading-1--line-height: 36px;
    --text-monospace-body: 14px;
    --text-monospace-body--font-weight: 400;
    --text-monospace-body--letter-spacing: 0em;
    --text-monospace-body--line-height: 20px;
    --text-custom-text: 14px;
    --text-custom-text--font-weight: 400;
    --text-custom-text--letter-spacing: 0em;
    --text-custom-text--line-height: 20px;
    --font-caption: 'Public Sans';
    --font-caption-bold: 'Public Sans';
    --font-body: 'Public Sans';
    --font-body-bold: 'Public Sans';
    --font-heading-3: 'Public Sans';
    --font-heading-2: 'Public Sans';
    --font-heading-1: 'Public Sans';
    --radius-full: 9999px;
    --spacing-112: 28rem;
  }
}
@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    border: 0 solid;
  }
  html, :host {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -moz-tab-size: 4;
      -o-tab-size: 4;
         tab-size: 4;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }
  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }
  abbr:where([title]) {
    -webkit-text-decoration: underline dotted;
    text-decoration: underline dotted;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }
  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }
  b, strong {
    font-weight: bolder;
  }
  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }
  small {
    font-size: 80%;
  }
  sub, sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
  }
  sub {
    bottom: -0.25em;
  }
  sup {
    top: -0.5em;
  }
  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }
  :-moz-focusring {
    outline: auto;
  }
  progress {
    vertical-align: baseline;
  }
  summary {
    display: list-item;
  }
  ol, ul, menu {
    list-style: none;
  }
  img, svg, video, canvas, audio, iframe, embed, object {
    display: block;
    vertical-align: middle;
  }
  img, video {
    max-width: 100%;
    height: auto;
  }
  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    border-radius: 0;
    background-color: transparent;
    opacity: 1;
  }
  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }
  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }
  ::file-selector-button {
    margin-inline-end: 4px;
  }
  ::-moz-placeholder {
    opacity: 1;
  }
  ::placeholder {
    opacity: 1;
  }
  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {
    ::-moz-placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
    ::placeholder {
      color: currentcolor;
      @supports (color: color-mix(in lab, red, red)) {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }
  textarea {
    resize: vertical;
  }
  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }
  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }
  ::-webkit-datetime-edit {
    display: inline-flex;
  }
  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }
  :-moz-ui-invalid {
    box-shadow: none;
  }
  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    -webkit-appearance: button;
       -moz-appearance: button;
            appearance: button;
  }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }
  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}
@layer utilities {
  .absolute {
    position: absolute;
  }
  .relative {
    position: relative;
  }
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .flex {
    display: flex;
  }
  .hidden {
    display: none;
  }
  .inline-block {
    display: inline-block;
  }
  .inline-flex {
    display: inline-flex;
  }
  .table {
    display: table;
  }
  .h-1\.5 {
    height: calc(var(--spacing) * 1.5);
  }
  .h-2 {
    height: calc(var(--spacing) * 2);
  }
  .h-3 {
    height: calc(var(--spacing) * 3);
  }
  .h-3\.5 {
    height: calc(var(--spacing) * 3.5);
  }
  .h-4 {
    height: calc(var(--spacing) * 4);
  }
  .h-5 {
    height: calc(var(--spacing) * 5);
  }
  .h-6 {
    height: calc(var(--spacing) * 6);
  }
  .h-7 {
    height: calc(var(--spacing) * 7);
  }
  .h-8 {
    height: calc(var(--spacing) * 8);
  }
  .h-9 {
    height: calc(var(--spacing) * 9);
  }
  .h-10 {
    height: calc(var(--spacing) * 10);
  }
  .h-12 {
    height: calc(var(--spacing) * 12);
  }
  .h-16 {
    height: calc(var(--spacing) * 16);
  }
  .h-52 {
    height: calc(var(--spacing) * 52);
  }
  .h-80 {
    height: calc(var(--spacing) * 80);
  }
  .h-auto {
    height: auto;
  }
  .h-full {
    height: 100%;
  }
  .h-px {
    height: 1px;
  }
  .h-screen {
    height: 100vh;
  }
  .max-h-\[90vh\] {
    max-height: 90vh;
  }
  .min-h-\[8px\] {
    min-height: 8px;
  }
  .min-h-\[96px\] {
    min-height: 96px;
  }
  .w-0\.5 {
    width: calc(var(--spacing) * 0.5);
  }
  .w-2 {
    width: calc(var(--spacing) * 2);
  }
  .w-3 {
    width: calc(var(--spacing) * 3);
  }
  .w-3\.5 {
    width: calc(var(--spacing) * 3.5);
  }
  .w-4 {
    width: calc(var(--spacing) * 4);
  }
  .w-5 {
    width: calc(var(--spacing) * 5);
  }
  .w-6 {
    width: calc(var(--spacing) * 6);
  }
  .w-7 {
    width: calc(var(--spacing) * 7);
  }
  .w-8 {
    width: calc(var(--spacing) * 8);
  }
  .w-9 {
    width: calc(var(--spacing) * 9);
  }
  .w-10 {
    width: calc(var(--spacing) * 10);
  }
  .w-12 {
    width: calc(var(--spacing) * 12);
  }
  .w-16 {
    width: calc(var(--spacing) * 16);
  }
  .w-20 {
    width: calc(var(--spacing) * 20);
  }
  .w-32 {
    width: calc(var(--spacing) * 32);
  }
  .w-52 {
    width: calc(var(--spacing) * 52);
  }
  .w-60 {
    width: calc(var(--spacing) * 60);
  }
  .w-64 {
    width: calc(var(--spacing) * 64);
  }
  .w-80 {
    width: calc(var(--spacing) * 80);
  }
  .w-96 {
    width: calc(var(--spacing) * 96);
  }
  .w-112 {
    width: var(--spacing-112);
  }
  .w-auto {
    width: auto;
  }
  .w-full {
    width: 100%;
  }
  .w-px {
    width: 1px;
  }
  .max-w-fit {
    max-width: -moz-fit-content;
    max-width: fit-content;
  }
  .min-w-\[192px\] {
    min-width: 192px;
  }
  .min-w-\[320px\] {
    min-width: 320px;
  }
  .flex-none {
    flex: none;
  }
  .shrink-0 {
    flex-shrink: 0;
  }
  .grow {
    flex-grow: 1;
  }
  .basis-0 {
    flex-basis: calc(var(--spacing) * 0);
  }
  .transform {
    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
  }
  .cursor-pointer {
    cursor: pointer;
  }
  .flex-col {
    flex-direction: column;
  }
  .flex-row {
    flex-direction: row;
  }
  .flex-nowrap {
    flex-wrap: nowrap;
  }
  .flex-wrap {
    flex-wrap: wrap;
  }
  .items-center {
    align-items: center;
  }
  .items-end {
    align-items: flex-end;
  }
  .items-start {
    align-items: flex-start;
  }
  .justify-between {
    justify-content: space-between;
  }
  .justify-center {
    justify-content: center;
  }
  .justify-end {
    justify-content: flex-end;
  }
  .gap-0\.5 {
    gap: calc(var(--spacing) * 0.5);
  }
  .gap-1 {
    gap: calc(var(--spacing) * 1);
  }
  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }
  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .self-stretch {
    align-self: stretch;
  }
  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .overflow-auto {
    overflow: auto;
  }
  .overflow-hidden {
    overflow: hidden;
  }
  .overflow-y-auto {
    overflow-y: auto;
  }
  .rounded {
    border-radius: 0.25rem;
  }
  .rounded-\[2px\] {
    border-radius: 2px;
  }
  .rounded-full {
    border-radius: calc(infinity * 1px);
  }
  .rounded-full {
    border-radius: var(--radius-full);
  }
  .rounded-lg {
    border-radius: var(--radius-lg);
  }
  .rounded-md {
    border-radius: var(--radius-md);
  }
  .rounded-sm {
    border-radius: var(--radius-sm);
  }
  .rounded-xl {
    border-radius: var(--radius-xl);
  }
  .rounded-t-lg {
    border-top-left-radius: var(--radius-lg);
    border-top-right-radius: var(--radius-lg);
  }
  .rounded-t-md {
    border-top-left-radius: var(--radius-md);
    border-top-right-radius: var(--radius-md);
  }
  .rounded-l-lg {
    border-top-left-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .rounded-r-lg {
    border-top-right-radius: var(--radius-lg);
    border-bottom-right-radius: var(--radius-lg);
  }
  .rounded-b-lg {
    border-bottom-right-radius: var(--radius-lg);
    border-bottom-left-radius: var(--radius-lg);
  }
  .rounded-b-md {
    border-bottom-right-radius: var(--radius-md);
    border-bottom-left-radius: var(--radius-md);
  }
  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }
  .border-r {
    border-right-style: var(--tw-border-style);
    border-right-width: 1px;
  }
  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }
  .border-b-2 {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 2px;
  }
  .border-l {
    border-left-style: var(--tw-border-style);
    border-left-width: 1px;
  }
  .border-none {
    --tw-border-style: none;
    border-style: none;
  }
  .border-solid {
    --tw-border-style: solid;
    border-style: solid;
  }
  .border-brand-100 {
    border-color: var(--color-brand-100);
  }
  .border-brand-600 {
    border-color: var(--color-brand-600);
  }
  .border-error-100 {
    border-color: var(--color-error-100);
  }
  .border-error-600 {
    border-color: var(--color-error-600);
  }
  .border-neutral-100 {
    border-color: var(--color-neutral-100);
  }
  .border-neutral-200 {
    border-color: var(--color-neutral-200);
  }
  .border-neutral-300 {
    border-color: var(--color-neutral-300);
  }
  .border-neutral-900 {
    border-color: var(--color-neutral-900);
  }
  .border-neutral-border {
    border-color: var(--color-neutral-border);
  }
  .border-success-100 {
    border-color: var(--color-success-100);
  }
  .border-warning-100 {
    border-color: var(--color-warning-100);
  }
  .bg-\[\#3e63ddff\] {
    background-color: #3e63ddff;
  }
  .bg-\[\#00000066\] {
    background-color: #00000066;
  }
  .bg-\[\#00000099\] {
    background-color: #00000099;
  }
  .bg-brand-50 {
    background-color: var(--color-brand-50);
  }
  .bg-brand-100 {
    background-color: var(--color-brand-100);
  }
  .bg-brand-600 {
    background-color: var(--color-brand-600);
  }
  .bg-brand-primary {
    background-color: var(--color-brand-primary);
  }
  .bg-default-background {
    background-color: var(--color-default-background);
  }
  .bg-error-50 {
    background-color: var(--color-error-50);
  }
  .bg-error-100 {
    background-color: var(--color-error-100);
  }
  .bg-error-600 {
    background-color: var(--color-error-600);
  }
  .bg-neutral-50 {
    background-color: var(--color-neutral-50);
  }
  .bg-neutral-100 {
    background-color: var(--color-neutral-100);
  }
  .bg-neutral-200 {
    background-color: var(--color-neutral-200);
  }
  .bg-neutral-300 {
    background-color: var(--color-neutral-300);
  }
  .bg-neutral-800 {
    background-color: var(--color-neutral-800);
  }
  .bg-neutral-border {
    background-color: var(--color-neutral-border);
  }
  .bg-success-50 {
    background-color: var(--color-success-50);
  }
  .bg-success-100 {
    background-color: var(--color-success-100);
  }
  .bg-transparent {
    background-color: transparent;
  }
  .bg-warning-50 {
    background-color: var(--color-warning-50);
  }
  .bg-warning-100 {
    background-color: var(--color-warning-100);
  }
  .bg-warning-500 {
    background-color: var(--color-warning-500);
  }
  .bg-white {
    background-color: var(--color-white);
  }
  .object-cover {
    -o-object-fit: cover;
       object-fit: cover;
  }
  .p-0 {
    padding: calc(var(--spacing) * 0);
  }
  .p-0\.5 {
    padding: calc(var(--spacing) * 0.5);
  }
  .px-0 {
    padding-inline: calc(var(--spacing) * 0);
  }
  .px-0\.5 {
    padding-inline: calc(var(--spacing) * 0.5);
  }
  .px-1 {
    padding-inline: calc(var(--spacing) * 1);
  }
  .px-2 {
    padding-inline: calc(var(--spacing) * 2);
  }
  .px-2\.5 {
    padding-inline: calc(var(--spacing) * 2.5);
  }
  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }
  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }
  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }
  .py-0 {
    padding-block: calc(var(--spacing) * 0);
  }
  .py-0\.5 {
    padding-block: calc(var(--spacing) * 0.5);
  }
  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }
  .py-1\.5 {
    padding-block: calc(var(--spacing) * 1.5);
  }
  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }
  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }
  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }
  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }
  .py-6 {
    padding-block: calc(var(--spacing) * 6);
  }
  .pt-0\.5 {
    padding-top: calc(var(--spacing) * 0.5);
  }
  .pt-1 {
    padding-top: calc(var(--spacing) * 1);
  }
  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }
  .pt-4 {
    padding-top: calc(var(--spacing) * 4);
  }
  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }
  .pr-2 {
    padding-right: calc(var(--spacing) * 2);
  }
  .pr-3 {
    padding-right: calc(var(--spacing) * 3);
  }
  .pb-1 {
    padding-bottom: calc(var(--spacing) * 1);
  }
  .pb-2 {
    padding-bottom: calc(var(--spacing) * 2);
  }
  .pb-3 {
    padding-bottom: calc(var(--spacing) * 3);
  }
  .pb-4 {
    padding-bottom: calc(var(--spacing) * 4);
  }
  .pb-px {
    padding-bottom: 1px;
  }
  .pl-1 {
    padding-left: calc(var(--spacing) * 1);
  }
  .pl-3 {
    padding-left: calc(var(--spacing) * 3);
  }
  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }
  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }
  .text-center {
    text-align: center;
  }
  .text-left {
    text-align: left;
  }
  .text-right {
    text-align: right;
  }
  .font-\[\'Inter\'\] {
    font-family: 'Inter';
  }
  .font-body {
    font-family: var(--font-body);
  }
  .font-body-bold {
    font-family: var(--font-body-bold);
  }
  .font-caption {
    font-family: var(--font-caption);
  }
  .font-caption-bold {
    font-family: var(--font-caption-bold);
  }
  .font-heading-1 {
    font-family: var(--font-heading-1);
  }
  .font-heading-2 {
    font-family: var(--font-heading-2);
  }
  .font-heading-3 {
    font-family: var(--font-heading-3);
  }
  .text-body {
    font-size: var(--text-body);
    line-height: var(--tw-leading, var(--text-body--line-height));
    letter-spacing: var(--tw-tracking, var(--text-body--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-body--font-weight));
  }
  .text-body-bold {
    font-size: var(--text-body-bold);
    line-height: var(--tw-leading, var(--text-body-bold--line-height));
    letter-spacing: var(--tw-tracking, var(--text-body-bold--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-body-bold--font-weight));
  }
  .text-caption {
    font-size: var(--text-caption);
    line-height: var(--tw-leading, var(--text-caption--line-height));
    letter-spacing: var(--tw-tracking, var(--text-caption--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-caption--font-weight));
  }
  .text-caption-bold {
    font-size: var(--text-caption-bold);
    line-height: var(--tw-leading, var(--text-caption-bold--line-height));
    letter-spacing: var(--tw-tracking, var(--text-caption-bold--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-caption-bold--font-weight));
  }
  .text-custom-text {
    font-size: var(--text-custom-text);
    line-height: var(--tw-leading, var(--text-custom-text--line-height));
    letter-spacing: var(--tw-tracking, var(--text-custom-text--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-custom-text--font-weight));
  }
  .text-heading-1 {
    font-size: var(--text-heading-1);
    line-height: var(--tw-leading, var(--text-heading-1--line-height));
    letter-spacing: var(--tw-tracking, var(--text-heading-1--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-heading-1--font-weight));
  }
  .text-heading-2 {
    font-size: var(--text-heading-2);
    line-height: var(--tw-leading, var(--text-heading-2--line-height));
    letter-spacing: var(--tw-tracking, var(--text-heading-2--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-heading-2--font-weight));
  }
  .text-heading-3 {
    font-size: var(--text-heading-3);
    line-height: var(--tw-leading, var(--text-heading-3--line-height));
    letter-spacing: var(--tw-tracking, var(--text-heading-3--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-heading-3--font-weight));
  }
  .text-monospace-body {
    font-size: var(--text-monospace-body);
    line-height: var(--tw-leading, var(--text-monospace-body--line-height));
    letter-spacing: var(--tw-tracking, var(--text-monospace-body--letter-spacing));
    font-weight: var(--tw-font-weight, var(--text-monospace-body--font-weight));
  }
  .text-\[10px\] {
    font-size: 10px;
  }
  .text-\[12px\] {
    font-size: 12px;
  }
  .text-\[14px\] {
    font-size: 14px;
  }
  .text-\[18px\] {
    font-size: 18px;
  }
  .text-\[24px\] {
    font-size: 24px;
  }
  .leading-\[10px\] {
    --tw-leading: 10px;
    line-height: 10px;
  }
  .leading-\[12px\] {
    --tw-leading: 12px;
    line-height: 12px;
  }
  .leading-\[14px\] {
    --tw-leading: 14px;
    line-height: 14px;
  }
  .leading-\[18px\] {
    --tw-leading: 18px;
    line-height: 18px;
  }
  .leading-\[20px\] {
    --tw-leading: 20px;
    line-height: 20px;
  }
  .leading-\[24px\] {
    --tw-leading: 24px;
    line-height: 24px;
  }
  .font-\[400\] {
    --tw-font-weight: 400;
    font-weight: 400;
  }
  .font-\[500\] {
    --tw-font-weight: 500;
    font-weight: 500;
  }
  .tracking-normal {
    --tw-tracking: var(--tracking-normal);
    letter-spacing: var(--tracking-normal);
  }
  .break-words {
    overflow-wrap: break-word;
  }
  .whitespace-nowrap {
    white-space: nowrap;
  }
  .whitespace-pre-wrap {
    white-space: pre-wrap;
  }
  .text-brand-600 {
    color: var(--color-brand-600);
  }
  .text-brand-700 {
    color: var(--color-brand-700);
  }
  .text-brand-800 {
    color: var(--color-brand-800);
  }
  .text-brand-900 {
    color: var(--color-brand-900);
  }
  .text-brand-primary {
    color: var(--color-brand-primary);
  }
  .text-default-font {
    color: var(--color-default-font);
  }
  .text-error-500 {
    color: var(--color-error-500);
  }
  .text-error-700 {
    color: var(--color-error-700);
  }
  .text-error-800 {
    color: var(--color-error-800);
  }
  .text-error-900 {
    color: var(--color-error-900);
  }
  .text-neutral-400 {
    color: var(--color-neutral-400);
  }
  .text-neutral-600 {
    color: var(--color-neutral-600);
  }
  .text-neutral-700 {
    color: var(--color-neutral-700);
  }
  .text-neutral-800 {
    color: var(--color-neutral-800);
  }
  .text-subtext-color {
    color: var(--color-subtext-color);
  }
  .text-success-700 {
    color: var(--color-success-700);
  }
  .text-success-800 {
    color: var(--color-success-800);
  }
  .text-success-900 {
    color: var(--color-success-900);
  }
  .text-warning-800 {
    color: var(--color-warning-800);
  }
  .text-warning-900 {
    color: var(--color-warning-900);
  }
  .text-white {
    color: var(--color-white);
  }
  .italic {
    font-style: italic;
  }
  .shadow-lg {
    --tw-shadow: 0px 12px 32px -4px var(--tw-shadow-color, rgba(0, 0, 0, 0.08)), 0px 4px 8px -2px var(--tw-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-md {
    --tw-shadow: 0px 4px 16px -2px var(--tw-shadow-color, rgba(0, 0, 0, 0.08)), 0px 2px 4px -1px var(--tw-shadow-color, rgba(0, 0, 0, 0.08));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .shadow-sm {
    --tw-shadow: 0px 1px 2px 0px var(--tw-shadow-color, rgba(0, 0, 0, 0.05));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }
  .outline {
    outline-style: var(--tw-outline-style);
    outline-width: 1px;
  }
  .outline-none {
    --tw-outline-style: none;
    outline-style: none;
  }
  .group-focus-within\/3816e3b5\:border-2 {
    &:is(:where(.group\/3816e3b5):focus-within *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
  .group-focus-within\/3816e3b5\:border-solid {
    &:is(:where(.group\/3816e3b5):focus-within *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-focus-within\/3816e3b5\:border-brand-600 {
    &:is(:where(.group\/3816e3b5):focus-within *) {
      border-color: var(--color-brand-600);
    }
  }
  .group-focus-within\/4ec05ee8\:border {
    &:is(:where(.group\/4ec05ee8):focus-within *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .group-focus-within\/4ec05ee8\:border-solid {
    &:is(:where(.group\/4ec05ee8):focus-within *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-focus-within\/4ec05ee8\:border-brand-primary {
    &:is(:where(.group\/4ec05ee8):focus-within *) {
      border-color: var(--color-brand-primary);
    }
  }
  .group-focus-within\/4ec05ee8\:bg-default-background {
    &:is(:where(.group\/4ec05ee8):focus-within *) {
      background-color: var(--color-default-background);
    }
  }
  .group-focus-within\/bb88f90b\:border {
    &:is(:where(.group\/bb88f90b):focus-within *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .group-focus-within\/bb88f90b\:border-solid {
    &:is(:where(.group\/bb88f90b):focus-within *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-focus-within\/bb88f90b\:border-brand-primary {
    &:is(:where(.group\/bb88f90b):focus-within *) {
      border-color: var(--color-brand-primary);
    }
  }
  .group-focus-within\/be48ca43\:border {
    &:is(:where(.group\/be48ca43):focus-within *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .group-focus-within\/be48ca43\:border-solid {
    &:is(:where(.group\/be48ca43):focus-within *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-focus-within\/be48ca43\:border-brand-primary {
    &:is(:where(.group\/be48ca43):focus-within *) {
      border-color: var(--color-brand-primary);
    }
  }
  .group-focus-within\/be48ca43\:bg-default-background {
    &:is(:where(.group\/be48ca43):focus-within *) {
      background-color: var(--color-default-background);
    }
  }
  .group-hover\/19b3e897\:bg-neutral-50 {
    &:is(:where(.group\/19b3e897):hover *) {
      @media (hover: hover) {
        background-color: var(--color-neutral-50);
      }
    }
  }
  .group-hover\/26cf85af\:text-brand-700 {
    &:is(:where(.group\/26cf85af):hover *) {
      @media (hover: hover) {
        color: var(--color-brand-700);
      }
    }
  }
  .group-hover\/26cf85af\:text-error-700 {
    &:is(:where(.group\/26cf85af):hover *) {
      @media (hover: hover) {
        color: var(--color-error-700);
      }
    }
  }
  .group-hover\/26cf85af\:text-neutral-700 {
    &:is(:where(.group\/26cf85af):hover *) {
      @media (hover: hover) {
        color: var(--color-neutral-700);
      }
    }
  }
  .group-hover\/26cf85af\:text-white {
    &:is(:where(.group\/26cf85af):hover *) {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .group-hover\/4ec05ee8\:border {
    &:is(:where(.group\/4ec05ee8):hover *) {
      @media (hover: hover) {
        border-style: var(--tw-border-style);
        border-width: 1px;
      }
    }
  }
  .group-hover\/4ec05ee8\:border-solid {
    &:is(:where(.group\/4ec05ee8):hover *) {
      @media (hover: hover) {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
  .group-hover\/4ec05ee8\:border-neutral-border {
    &:is(:where(.group\/4ec05ee8):hover *) {
      @media (hover: hover) {
        border-color: var(--color-neutral-border);
      }
    }
  }
  .group-hover\/56dea6ed\:text-default-font {
    &:is(:where(.group\/56dea6ed):hover *) {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-hover\/56dea6ed\:text-neutral-400 {
    &:is(:where(.group\/56dea6ed):hover *) {
      @media (hover: hover) {
        color: var(--color-neutral-400);
      }
    }
  }
  .group-hover\/73855188\:text-default-font {
    &:is(:where(.group\/73855188):hover *) {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-hover\/73855188\:text-neutral-400 {
    &:is(:where(.group\/73855188):hover *) {
      @media (hover: hover) {
        color: var(--color-neutral-400);
      }
    }
  }
  .group-hover\/9358ee78\:text-default-font {
    &:is(:where(.group\/9358ee78):hover *) {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-hover\/969e345b\:hidden {
    &:is(:where(.group\/969e345b):hover *) {
      @media (hover: hover) {
        display: none;
      }
    }
  }
  .group-hover\/981899b7\:text-default-font {
    &:is(:where(.group\/981899b7):hover *) {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-hover\/a4ee726a\:text-brand-700 {
    &:is(:where(.group\/a4ee726a):hover *) {
      @media (hover: hover) {
        color: var(--color-brand-700);
      }
    }
  }
  .group-hover\/a4ee726a\:text-white {
    &:is(:where(.group\/a4ee726a):hover *) {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .group-hover\/a4ee726a\:underline {
    &:is(:where(.group\/a4ee726a):hover *) {
      @media (hover: hover) {
        text-decoration-line: underline;
      }
    }
  }
  .group-hover\/adcae8d6\:text-default-font {
    &:is(:where(.group\/adcae8d6):hover *) {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-hover\/af9405b1\:text-brand-700 {
    &:is(:where(.group\/af9405b1):hover *) {
      @media (hover: hover) {
        color: var(--color-brand-700);
      }
    }
  }
  .group-hover\/af9405b1\:text-error-700 {
    &:is(:where(.group\/af9405b1):hover *) {
      @media (hover: hover) {
        color: var(--color-error-700);
      }
    }
  }
  .group-hover\/af9405b1\:text-neutral-700 {
    &:is(:where(.group\/af9405b1):hover *) {
      @media (hover: hover) {
        color: var(--color-neutral-700);
      }
    }
  }
  .group-hover\/af9405b1\:text-white {
    &:is(:where(.group\/af9405b1):hover *) {
      @media (hover: hover) {
        color: var(--color-white);
      }
    }
  }
  .group-hover\/bb88f90b\:border {
    &:is(:where(.group\/bb88f90b):hover *) {
      @media (hover: hover) {
        border-style: var(--tw-border-style);
        border-width: 1px;
      }
    }
  }
  .group-hover\/bb88f90b\:border-solid {
    &:is(:where(.group\/bb88f90b):hover *) {
      @media (hover: hover) {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
  .group-hover\/bb88f90b\:border-neutral-border {
    &:is(:where(.group\/bb88f90b):hover *) {
      @media (hover: hover) {
        border-color: var(--color-neutral-border);
      }
    }
  }
  .group-hover\/bb88f90b\:bg-neutral-100 {
    &:is(:where(.group\/bb88f90b):hover *) {
      @media (hover: hover) {
        background-color: var(--color-neutral-100);
      }
    }
  }
  .group-hover\/be48ca43\:border {
    &:is(:where(.group\/be48ca43):hover *) {
      @media (hover: hover) {
        border-style: var(--tw-border-style);
        border-width: 1px;
      }
    }
  }
  .group-hover\/be48ca43\:border-solid {
    &:is(:where(.group\/be48ca43):hover *) {
      @media (hover: hover) {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
  .group-hover\/be48ca43\:border-neutral-border {
    &:is(:where(.group\/be48ca43):hover *) {
      @media (hover: hover) {
        border-color: var(--color-neutral-border);
      }
    }
  }
  .group-hover\/c1145464\:text-default-font {
    &:is(:where(.group\/c1145464):hover *) {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-hover\/c841484c\:bg-neutral-50 {
    &:is(:where(.group\/c841484c):hover *) {
      @media (hover: hover) {
        background-color: var(--color-neutral-50);
      }
    }
  }
  .group-hover\/d5612535\:text-brand-700 {
    &:is(:where(.group\/d5612535):hover *) {
      @media (hover: hover) {
        color: var(--color-brand-700);
      }
    }
  }
  .group-hover\/d5612535\:text-default-font {
    &:is(:where(.group\/d5612535):hover *) {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-hover\/d5612535\:text-neutral-400 {
    &:is(:where(.group\/d5612535):hover *) {
      @media (hover: hover) {
        color: var(--color-neutral-400);
      }
    }
  }
  .group-hover\/e50471c1\:text-default-font {
    &:is(:where(.group\/e50471c1):hover *) {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-hover\/e8c76626\:text-default-font {
    &:is(:where(.group\/e8c76626):hover *) {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-active\/0f804ad9\:border-2 {
    &:is(:where(.group\/0f804ad9):active *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
  .group-active\/0f804ad9\:border-solid {
    &:is(:where(.group\/0f804ad9):active *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-active\/0f804ad9\:border-brand-700 {
    &:is(:where(.group\/0f804ad9):active *) {
      border-color: var(--color-brand-700);
    }
  }
  .group-active\/26cf85af\:text-brand-700 {
    &:is(:where(.group\/26cf85af):active *) {
      color: var(--color-brand-700);
    }
  }
  .group-active\/26cf85af\:text-error-700 {
    &:is(:where(.group\/26cf85af):active *) {
      color: var(--color-error-700);
    }
  }
  .group-active\/26cf85af\:text-neutral-700 {
    &:is(:where(.group\/26cf85af):active *) {
      color: var(--color-neutral-700);
    }
  }
  .group-active\/26cf85af\:text-white {
    &:is(:where(.group\/26cf85af):active *) {
      color: var(--color-white);
    }
  }
  .group-active\/3816e3b5\:border-2 {
    &:is(:where(.group\/3816e3b5):active *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
  .group-active\/3816e3b5\:border-solid {
    &:is(:where(.group\/3816e3b5):active *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-active\/3816e3b5\:border-brand-600 {
    &:is(:where(.group\/3816e3b5):active *) {
      border-color: var(--color-brand-600);
    }
  }
  .group-active\/56dea6ed\:text-default-font {
    &:is(:where(.group\/56dea6ed):active *) {
      color: var(--color-default-font);
    }
  }
  .group-active\/56dea6ed\:text-neutral-400 {
    &:is(:where(.group\/56dea6ed):active *) {
      color: var(--color-neutral-400);
    }
  }
  .group-active\/73855188\:text-default-font {
    &:is(:where(.group\/73855188):active *) {
      color: var(--color-default-font);
    }
  }
  .group-active\/73855188\:text-neutral-400 {
    &:is(:where(.group\/73855188):active *) {
      color: var(--color-neutral-400);
    }
  }
  .group-active\/981899b7\:text-default-font {
    &:is(:where(.group\/981899b7):active *) {
      color: var(--color-default-font);
    }
  }
  .group-active\/af9405b1\:text-brand-700 {
    &:is(:where(.group\/af9405b1):active *) {
      color: var(--color-brand-700);
    }
  }
  .group-active\/af9405b1\:text-error-700 {
    &:is(:where(.group\/af9405b1):active *) {
      color: var(--color-error-700);
    }
  }
  .group-active\/af9405b1\:text-neutral-700 {
    &:is(:where(.group\/af9405b1):active *) {
      color: var(--color-neutral-700);
    }
  }
  .group-active\/af9405b1\:text-white {
    &:is(:where(.group\/af9405b1):active *) {
      color: var(--color-white);
    }
  }
  .group-disabled\/0f804ad9\:border-2 {
    &:is(:where(.group\/0f804ad9):disabled *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
  .group-disabled\/0f804ad9\:border-solid {
    &:is(:where(.group\/0f804ad9):disabled *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-disabled\/0f804ad9\:border-neutral-200 {
    &:is(:where(.group\/0f804ad9):disabled *) {
      border-color: var(--color-neutral-200);
    }
  }
  .group-disabled\/0f804ad9\:bg-neutral-100 {
    &:is(:where(.group\/0f804ad9):disabled *) {
      background-color: var(--color-neutral-100);
    }
  }
  .group-disabled\/0f804ad9\:bg-neutral-200 {
    &:is(:where(.group\/0f804ad9):disabled *) {
      background-color: var(--color-neutral-200);
    }
  }
  .group-disabled\/0f804ad9\:text-subtext-color {
    &:is(:where(.group\/0f804ad9):disabled *) {
      color: var(--color-subtext-color);
    }
  }
  .group-active\/0f804ad9\:group-disabled\/0f804ad9\:border-2 {
    &:is(:where(.group\/0f804ad9):active *) {
      &:is(:where(.group\/0f804ad9):disabled *) {
        border-style: var(--tw-border-style);
        border-width: 2px;
      }
    }
  }
  .group-active\/0f804ad9\:group-disabled\/0f804ad9\:border-solid {
    &:is(:where(.group\/0f804ad9):active *) {
      &:is(:where(.group\/0f804ad9):disabled *) {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
  .group-active\/0f804ad9\:group-disabled\/0f804ad9\:border-neutral-200 {
    &:is(:where(.group\/0f804ad9):active *) {
      &:is(:where(.group\/0f804ad9):disabled *) {
        border-color: var(--color-neutral-200);
      }
    }
  }
  .group-disabled\/26cf85af\:text-neutral-400 {
    &:is(:where(.group\/26cf85af):disabled *) {
      color: var(--color-neutral-400);
    }
  }
  .group-disabled\/3816e3b5\:border-2 {
    &:is(:where(.group\/3816e3b5):disabled *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
  .group-disabled\/3816e3b5\:border-solid {
    &:is(:where(.group\/3816e3b5):disabled *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-disabled\/3816e3b5\:border-neutral-200 {
    &:is(:where(.group\/3816e3b5):disabled *) {
      border-color: var(--color-neutral-200);
    }
  }
  .group-disabled\/3816e3b5\:bg-neutral-100 {
    &:is(:where(.group\/3816e3b5):disabled *) {
      background-color: var(--color-neutral-100);
    }
  }
  .group-disabled\/3816e3b5\:text-neutral-400 {
    &:is(:where(.group\/3816e3b5):disabled *) {
      color: var(--color-neutral-400);
    }
  }
  .group-disabled\/3816e3b5\:text-subtext-color {
    &:is(:where(.group\/3816e3b5):disabled *) {
      color: var(--color-subtext-color);
    }
  }
  .group-active\/3816e3b5\:group-disabled\/3816e3b5\:border-2 {
    &:is(:where(.group\/3816e3b5):active *) {
      &:is(:where(.group\/3816e3b5):disabled *) {
        border-style: var(--tw-border-style);
        border-width: 2px;
      }
    }
  }
  .group-active\/3816e3b5\:group-disabled\/3816e3b5\:border-solid {
    &:is(:where(.group\/3816e3b5):active *) {
      &:is(:where(.group\/3816e3b5):disabled *) {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
  .group-active\/3816e3b5\:group-disabled\/3816e3b5\:border-neutral-200 {
    &:is(:where(.group\/3816e3b5):active *) {
      &:is(:where(.group\/3816e3b5):disabled *) {
        border-color: var(--color-neutral-200);
      }
    }
  }
  .group-disabled\/3b777358\:text-neutral-400 {
    &:is(:where(.group\/3b777358):disabled *) {
      color: var(--color-neutral-400);
    }
  }
  .group-disabled\/45fe0312\:text-neutral-400 {
    &:is(:where(.group\/45fe0312):disabled *) {
      color: var(--color-neutral-400);
    }
  }
  .group-disabled\/502d4919\:border-2 {
    &:is(:where(.group\/502d4919):disabled *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
  .group-disabled\/502d4919\:border-solid {
    &:is(:where(.group\/502d4919):disabled *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-disabled\/502d4919\:border-neutral-300 {
    &:is(:where(.group\/502d4919):disabled *) {
      border-color: var(--color-neutral-300);
    }
  }
  .group-disabled\/502d4919\:bg-neutral-100 {
    &:is(:where(.group\/502d4919):disabled *) {
      background-color: var(--color-neutral-100);
    }
  }
  .group-disabled\/502d4919\:bg-neutral-300 {
    &:is(:where(.group\/502d4919):disabled *) {
      background-color: var(--color-neutral-300);
    }
  }
  .group-disabled\/a4ee726a\:text-neutral-400 {
    &:is(:where(.group\/a4ee726a):disabled *) {
      color: var(--color-neutral-400);
    }
  }
  .group-hover\/a4ee726a\:group-disabled\/a4ee726a\:text-neutral-400 {
    &:is(:where(.group\/a4ee726a):hover *) {
      @media (hover: hover) {
        &:is(:where(.group\/a4ee726a):disabled *) {
          color: var(--color-neutral-400);
        }
      }
    }
  }
  .group-hover\/a4ee726a\:group-disabled\/a4ee726a\:no-underline {
    &:is(:where(.group\/a4ee726a):hover *) {
      @media (hover: hover) {
        &:is(:where(.group\/a4ee726a):disabled *) {
          text-decoration-line: none;
        }
      }
    }
  }
  .group-disabled\/af9405b1\:text-neutral-400 {
    &:is(:where(.group\/af9405b1):disabled *) {
      color: var(--color-neutral-400);
    }
  }
  .group-disabled\/de0b4dfb\:border-2 {
    &:is(:where(.group\/de0b4dfb):disabled *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
  .group-disabled\/de0b4dfb\:border-solid {
    &:is(:where(.group\/de0b4dfb):disabled *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-disabled\/de0b4dfb\:border-neutral-200 {
    &:is(:where(.group\/de0b4dfb):disabled *) {
      border-color: var(--color-neutral-200);
    }
  }
  .group-disabled\/de0b4dfb\:bg-neutral-100 {
    &:is(:where(.group\/de0b4dfb):disabled *) {
      background-color: var(--color-neutral-100);
    }
  }
  .group-disabled\/de0b4dfb\:text-neutral-400 {
    &:is(:where(.group\/de0b4dfb):disabled *) {
      color: var(--color-neutral-400);
    }
  }
  .group-aria-\[checked\=true\]\/0f804ad9\:flex {
    &:is(:where(.group\/0f804ad9)[aria-checked="true"] *) {
      display: flex;
    }
  }
  .group-aria-\[checked\=true\]\/0f804ad9\:border-2 {
    &:is(:where(.group\/0f804ad9)[aria-checked="true"] *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
  .group-aria-\[checked\=true\]\/0f804ad9\:border-solid {
    &:is(:where(.group\/0f804ad9)[aria-checked="true"] *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-aria-\[checked\=true\]\/0f804ad9\:border-brand-600 {
    &:is(:where(.group\/0f804ad9)[aria-checked="true"] *) {
      border-color: var(--color-brand-600);
    }
  }
  .group-aria-\[checked\=true\]\/0f804ad9\:bg-brand-600 {
    &:is(:where(.group\/0f804ad9)[aria-checked="true"] *) {
      background-color: var(--color-brand-600);
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:inline-flex {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      display: inline-flex;
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:border {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:border-solid {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:border-brand-600 {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      border-color: var(--color-brand-600);
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:bg-brand-600 {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      background-color: var(--color-brand-600);
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:font-\[\'Inter\'\] {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      font-family: 'Inter';
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:text-\[14px\] {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      font-size: 14px;
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:leading-\[14px\] {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      --tw-leading: 14px;
      line-height: 14px;
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:font-\[600\] {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      --tw-font-weight: 600;
      font-weight: 600;
    }
  }
  .group-aria-\[checked\=true\]\/3816e3b5\:tracking-normal {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      --tw-tracking: var(--tracking-normal);
      letter-spacing: var(--tracking-normal);
    }
  }
  .group-focus-within\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-2 {
    &:is(:where(.group\/3816e3b5):focus-within *) {
      &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
        border-style: var(--tw-border-style);
        border-width: 2px;
      }
    }
  }
  .group-focus-within\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-solid {
    &:is(:where(.group\/3816e3b5):focus-within *) {
      &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
  .group-focus-within\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-brand-500 {
    &:is(:where(.group\/3816e3b5):focus-within *) {
      &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
        border-color: var(--color-brand-500);
      }
    }
  }
  .group-focus-within\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:bg-brand-500 {
    &:is(:where(.group\/3816e3b5):focus-within *) {
      &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
        background-color: var(--color-brand-500);
      }
    }
  }
  .group-active\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-2 {
    &:is(:where(.group\/3816e3b5):active *) {
      &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
        border-style: var(--tw-border-style);
        border-width: 2px;
      }
    }
  }
  .group-active\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-solid {
    &:is(:where(.group\/3816e3b5):active *) {
      &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
  .group-active\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-brand-500 {
    &:is(:where(.group\/3816e3b5):active *) {
      &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
        border-color: var(--color-brand-500);
      }
    }
  }
  .group-active\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:bg-brand-500 {
    &:is(:where(.group\/3816e3b5):active *) {
      &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
        background-color: var(--color-brand-500);
      }
    }
  }
  .group-aria-\[checked\=true\]\/502d4919\:flex {
    &:is(:where(.group\/502d4919)[aria-checked="true"] *) {
      display: flex;
    }
  }
  .group-aria-\[checked\=true\]\/502d4919\:border-2 {
    &:is(:where(.group\/502d4919)[aria-checked="true"] *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
  .group-aria-\[checked\=true\]\/502d4919\:border-solid {
    &:is(:where(.group\/502d4919)[aria-checked="true"] *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-aria-\[checked\=true\]\/502d4919\:border-brand-600 {
    &:is(:where(.group\/502d4919)[aria-checked="true"] *) {
      border-color: var(--color-brand-600);
    }
  }
  .group-aria-\[checked\=true\]\/502d4919\:bg-brand-600 {
    &:is(:where(.group\/502d4919)[aria-checked="true"] *) {
      background-color: var(--color-brand-600);
    }
  }
  .group-aria-\[checked\=true\]\/56dea6ed\:text-default-font {
    &:is(:where(.group\/56dea6ed)[aria-checked="true"] *) {
      color: var(--color-default-font);
    }
  }
  .group-aria-\[checked\=true\]\/73855188\:text-default-font {
    &:is(:where(.group\/73855188)[aria-checked="true"] *) {
      color: var(--color-default-font);
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:inline-flex {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      display: inline-flex;
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:border {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:border-solid {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:border-brand-600 {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      border-color: var(--color-brand-600);
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:bg-brand-600 {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      background-color: var(--color-brand-600);
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:font-\[\'Inter\'\] {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      font-family: 'Inter';
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:text-\[16px\] {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      font-size: 16px;
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:leading-\[16px\] {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      --tw-leading: 16px;
      line-height: 16px;
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:font-\[400\] {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      --tw-font-weight: 400;
      font-weight: 400;
    }
  }
  .group-aria-\[checked\=true\]\/de0b4dfb\:tracking-normal {
    &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
      --tw-tracking: var(--tracking-normal);
      letter-spacing: var(--tracking-normal);
    }
  }
  .group-data-\[state\=checked\]\/969e345b\:inline-flex {
    &:is(:where(.group\/969e345b)[data-state="checked"] *) {
      display: inline-flex;
    }
  }
  .group-data-\[state\=checked\]\/969e345b\:text-brand-600 {
    &:is(:where(.group\/969e345b)[data-state="checked"] *) {
      color: var(--color-brand-600);
    }
  }
  .group-data-\[state\=open\]\/d2e81e20\:h-auto {
    &:is(:where(.group\/d2e81e20)[data-state="open"] *) {
      height: auto;
    }
  }
  .group-data-\[state\=open\]\/d2e81e20\:w-full {
    &:is(:where(.group\/d2e81e20)[data-state="open"] *) {
      width: 100%;
    }
  }
  .group-data-\[state\=open\]\/d2e81e20\:flex-none {
    &:is(:where(.group\/d2e81e20)[data-state="open"] *) {
      flex: none;
    }
  }
  .group-\[\.outside\]\:bg-transparent {
    &:is(:where(.group):is(.outside) *) {
      background-color: transparent;
    }
  }
  .group-\[\.outside\]\:\!text-neutral-400 {
    &:is(:where(.group):is(.outside) *) {
      color: var(--color-neutral-400) !important;
    }
  }
  .group-\[\.outside\.range-end\]\:rounded-l-none {
    &:is(:where(.group):is(.outside.range-end) *) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .group-\[\.outside\.range-end\]\:rounded-r-lg {
    &:is(:where(.group):is(.outside.range-end) *) {
      border-top-right-radius: var(--radius-lg);
      border-bottom-right-radius: var(--radius-lg);
    }
  }
  .group-\[\.outside\.range-end\]\:bg-neutral-100 {
    &:is(:where(.group):is(.outside.range-end) *) {
      background-color: var(--color-neutral-100);
    }
  }
  .group-\[\.outside\.range-start\]\:rounded-l-lg {
    &:is(:where(.group):is(.outside.range-start) *) {
      border-top-left-radius: var(--radius-lg);
      border-bottom-left-radius: var(--radius-lg);
    }
  }
  .group-\[\.outside\.range-start\]\:rounded-r-none {
    &:is(:where(.group):is(.outside.range-start) *) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }
  .group-\[\.outside\.range-start\]\:bg-neutral-100 {
    &:is(:where(.group):is(.outside.range-start) *) {
      background-color: var(--color-neutral-100);
    }
  }
  .group-\[\.outside\.selected\]\:bg-neutral-100 {
    &:is(:where(.group):is(.outside.selected) *) {
      background-color: var(--color-neutral-100);
    }
  }
  .group-\[\.range-middle\.selected\]\:rounded-none {
    &:is(:where(.group):is(.range-middle.selected) *) {
      border-radius: 0;
    }
  }
  .group-\[\.range-middle\.selected\]\:bg-neutral-100 {
    &:is(:where(.group):is(.range-middle.selected) *) {
      background-color: var(--color-neutral-100);
    }
  }
  .group-\[\.range-middle\.selected\]\:text-default-font {
    &:is(:where(.group):is(.range-middle.selected) *) {
      color: var(--color-default-font);
    }
  }
  .group-\[\.selected\]\:bg-brand-600 {
    &:is(:where(.group):is(.selected) *) {
      background-color: var(--color-brand-600);
    }
  }
  .group-\[\.selected\]\:text-white {
    &:is(:where(.group):is(.selected) *) {
      color: var(--color-white);
    }
  }
  .placeholder\:text-neutral-400 {
    &::-moz-placeholder {
      color: var(--color-neutral-400);
    }
    &::placeholder {
      color: var(--color-neutral-400);
    }
  }
  .hover\:rounded-sm {
    &:hover {
      @media (hover: hover) {
        border-radius: var(--radius-sm);
      }
    }
  }
  .hover\:border {
    &:hover {
      @media (hover: hover) {
        border-style: var(--tw-border-style);
        border-width: 1px;
      }
    }
  }
  .hover\:border-b-2 {
    &:hover {
      @media (hover: hover) {
        border-bottom-style: var(--tw-border-style);
        border-bottom-width: 2px;
      }
    }
  }
  .hover\:border-none {
    &:hover {
      @media (hover: hover) {
        --tw-border-style: none;
        border-style: none;
      }
    }
  }
  .hover\:border-solid {
    &:hover {
      @media (hover: hover) {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
  .hover\:border-brand-600 {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-brand-600);
      }
    }
  }
  .hover\:border-neutral-border {
    &:hover {
      @media (hover: hover) {
        border-color: var(--color-neutral-border);
      }
    }
  }
  .hover\:bg-\[\#ffffff29\] {
    &:hover {
      @media (hover: hover) {
        background-color: #ffffff29;
      }
    }
  }
  .hover\:bg-brand-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-brand-50);
      }
    }
  }
  .hover\:bg-brand-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-brand-100);
      }
    }
  }
  .hover\:bg-brand-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-brand-500);
      }
    }
  }
  .hover\:bg-error-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-error-50);
      }
    }
  }
  .hover\:bg-error-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-error-100);
      }
    }
  }
  .hover\:bg-error-500 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-error-500);
      }
    }
  }
  .hover\:bg-neutral-50 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-neutral-50);
      }
    }
  }
  .hover\:bg-neutral-100 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-neutral-100);
      }
    }
  }
  .hover\:bg-neutral-200 {
    &:hover {
      @media (hover: hover) {
        background-color: var(--color-neutral-200);
      }
    }
  }
  .hover\:bg-transparent {
    &:hover {
      @media (hover: hover) {
        background-color: transparent;
      }
    }
  }
  .hover\:text-default-font {
    &:hover {
      @media (hover: hover) {
        color: var(--color-default-font);
      }
    }
  }
  .group-\[\.outside\]\:hover\:bg-neutral-100 {
    &:is(:where(.group):is(.outside) *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-neutral-100);
        }
      }
    }
  }
  .group-\[\.range-middle\.selected\]\:hover\:bg-neutral-100 {
    &:is(:where(.group):is(.range-middle.selected) *) {
      &:hover {
        @media (hover: hover) {
          background-color: var(--color-neutral-100);
        }
      }
    }
  }
  .active\:rounded-sm {
    &:active {
      border-radius: var(--radius-sm);
    }
  }
  .active\:border-none {
    &:active {
      --tw-border-style: none;
      border-style: none;
    }
  }
  .active\:bg-\[\#ffffff3d\] {
    &:active {
      background-color: #ffffff3d;
    }
  }
  .active\:bg-brand-50 {
    &:active {
      background-color: var(--color-brand-50);
    }
  }
  .active\:bg-brand-100 {
    &:active {
      background-color: var(--color-brand-100);
    }
  }
  .active\:bg-brand-600 {
    &:active {
      background-color: var(--color-brand-600);
    }
  }
  .active\:bg-default-background {
    &:active {
      background-color: var(--color-default-background);
    }
  }
  .active\:bg-error-50 {
    &:active {
      background-color: var(--color-error-50);
    }
  }
  .active\:bg-error-100 {
    &:active {
      background-color: var(--color-error-100);
    }
  }
  .active\:bg-error-600 {
    &:active {
      background-color: var(--color-error-600);
    }
  }
  .active\:bg-neutral-50 {
    &:active {
      background-color: var(--color-neutral-50);
    }
  }
  .active\:bg-neutral-100 {
    &:active {
      background-color: var(--color-neutral-100);
    }
  }
  .active\:bg-neutral-200 {
    &:active {
      background-color: var(--color-neutral-200);
    }
  }
  .active\:bg-transparent {
    &:active {
      background-color: transparent;
    }
  }
  .active\:bg-white {
    &:active {
      background-color: var(--color-white);
    }
  }
  .disabled\:cursor-default {
    &:disabled {
      cursor: default;
    }
  }
  .disabled\:border {
    &:disabled {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .disabled\:border-solid {
    &:disabled {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .disabled\:border-neutral-100 {
    &:disabled {
      border-color: var(--color-neutral-100);
    }
  }
  .disabled\:bg-neutral-50 {
    &:disabled {
      background-color: var(--color-neutral-50);
    }
  }
  .disabled\:bg-neutral-100 {
    &:disabled {
      background-color: var(--color-neutral-100);
    }
  }
  .disabled\:bg-neutral-200 {
    &:disabled {
      background-color: var(--color-neutral-200);
    }
  }
  .hover\:disabled\:cursor-default {
    &:hover {
      @media (hover: hover) {
        &:disabled {
          cursor: default;
        }
      }
    }
  }
  .hover\:disabled\:border {
    &:hover {
      @media (hover: hover) {
        &:disabled {
          border-style: var(--tw-border-style);
          border-width: 1px;
        }
      }
    }
  }
  .hover\:disabled\:border-solid {
    &:hover {
      @media (hover: hover) {
        &:disabled {
          --tw-border-style: solid;
          border-style: solid;
        }
      }
    }
  }
  .hover\:disabled\:border-neutral-100 {
    &:hover {
      @media (hover: hover) {
        &:disabled {
          border-color: var(--color-neutral-100);
        }
      }
    }
  }
  .hover\:disabled\:bg-neutral-50 {
    &:hover {
      @media (hover: hover) {
        &:disabled {
          background-color: var(--color-neutral-50);
        }
      }
    }
  }
  .hover\:disabled\:bg-neutral-100 {
    &:hover {
      @media (hover: hover) {
        &:disabled {
          background-color: var(--color-neutral-100);
        }
      }
    }
  }
  .hover\:disabled\:bg-neutral-200 {
    &:hover {
      @media (hover: hover) {
        &:disabled {
          background-color: var(--color-neutral-200);
        }
      }
    }
  }
  .active\:disabled\:cursor-default {
    &:active {
      &:disabled {
        cursor: default;
      }
    }
  }
  .active\:disabled\:bg-neutral-100 {
    &:active {
      &:disabled {
        background-color: var(--color-neutral-100);
      }
    }
  }
  .active\:disabled\:bg-neutral-200 {
    &:active {
      &:disabled {
        background-color: var(--color-neutral-200);
      }
    }
  }
  .aria-\[checked\=true\]\:border {
    &[aria-checked="true"] {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
  .aria-\[checked\=true\]\:border-solid {
    &[aria-checked="true"] {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
  .aria-\[checked\=true\]\:border-brand-200 {
    &[aria-checked="true"] {
      border-color: var(--color-brand-200);
    }
  }
  .aria-\[checked\=true\]\:border-brand-600 {
    &[aria-checked="true"] {
      border-color: var(--color-brand-600);
    }
  }
  .aria-\[checked\=true\]\:bg-brand-50 {
    &[aria-checked="true"] {
      background-color: var(--color-brand-50);
    }
  }
  .aria-\[checked\=true\]\:bg-brand-600 {
    &[aria-checked="true"] {
      background-color: var(--color-brand-600);
    }
  }
  .aria-\[checked\=true\]\:bg-default-background {
    &[aria-checked="true"] {
      background-color: var(--color-default-background);
    }
  }
  .aria-\[checked\=true\]\:shadow-sm {
    &[aria-checked="true"] {
      --tw-shadow: 0px 1px 2px 0px var(--tw-shadow-color, rgba(0, 0, 0, 0.05));
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }
  .hover\:aria-\[checked\=true\]\:border {
    &:hover {
      @media (hover: hover) {
        &[aria-checked="true"] {
          border-style: var(--tw-border-style);
          border-width: 1px;
        }
      }
    }
  }
  .hover\:aria-\[checked\=true\]\:border-solid {
    &:hover {
      @media (hover: hover) {
        &[aria-checked="true"] {
          --tw-border-style: solid;
          border-style: solid;
        }
      }
    }
  }
  .hover\:aria-\[checked\=true\]\:border-brand-200 {
    &:hover {
      @media (hover: hover) {
        &[aria-checked="true"] {
          border-color: var(--color-brand-200);
        }
      }
    }
  }
  .hover\:aria-\[checked\=true\]\:bg-brand-50 {
    &:hover {
      @media (hover: hover) {
        &[aria-checked="true"] {
          background-color: var(--color-brand-50);
        }
      }
    }
  }
  .hover\:aria-\[checked\=true\]\:bg-default-background {
    &:hover {
      @media (hover: hover) {
        &[aria-checked="true"] {
          background-color: var(--color-default-background);
        }
      }
    }
  }
  .active\:aria-\[checked\=true\]\:bg-default-background {
    &:active {
      &[aria-checked="true"] {
        background-color: var(--color-default-background);
      }
    }
  }
  .data-\[highlighted\]\:bg-brand-50 {
    &[data-highlighted] {
      background-color: var(--color-brand-50);
    }
  }
  .data-\[highlighted\]\:bg-neutral-100 {
    &[data-highlighted] {
      background-color: var(--color-neutral-100);
    }
  }
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-tracking {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}
@property --tw-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false;
}
@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-ring-inset {
  syntax: "*";
  inherits: false;
}
@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0px;
}
@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}
@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-tracking: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-outline-style: solid;
    }
  }
}
