/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */
@layer properties;
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.flex {
  display: flex;
}
.hidden {
  display: none;
}
.inline-block {
  display: inline-block;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.h-screen {
  height: 100vh;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.min-h-\[8px\] {
  min-height: 8px;
}
.min-h-\[96px\] {
  min-height: 96px;
}
.w-auto {
  width: auto;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.max-w-fit {
  max-width: -moz-fit-content;
  max-width: fit-content;
}
.min-w-\[192px\] {
  min-width: 192px;
}
.min-w-\[320px\] {
  min-width: 320px;
}
.flex-none {
  flex: none;
}
.shrink-0 {
  flex-shrink: 0;
}
.grow {
  flex-grow: 1;
}
.transform {
  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);
}
.cursor-pointer {
  cursor: pointer;
}
.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-center {
  align-items: center;
}
.items-end {
  align-items: flex-end;
}
.items-start {
  align-items: flex-start;
}
.justify-between {
  justify-content: space-between;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: flex-end;
}
.self-stretch {
  align-self: stretch;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.rounded-\[2px\] {
  border-radius: 2px;
}
.rounded-full {
  border-radius: calc(infinity * 1px);
}
.border {
  border-style: var(--tw-border-style);
  border-width: 1px;
}
.border-2 {
  border-style: var(--tw-border-style);
  border-width: 2px;
}
.border-t {
  border-top-style: var(--tw-border-style);
  border-top-width: 1px;
}
.border-r {
  border-right-style: var(--tw-border-style);
  border-right-width: 1px;
}
.border-b {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 1px;
}
.border-b-2 {
  border-bottom-style: var(--tw-border-style);
  border-bottom-width: 2px;
}
.border-l {
  border-left-style: var(--tw-border-style);
  border-left-width: 1px;
}
.border-none {
  --tw-border-style: none;
  border-style: none;
}
.border-solid {
  --tw-border-style: solid;
  border-style: solid;
}
.bg-\[\#3e63ddff\] {
  background-color: #3e63ddff;
}
.bg-\[\#00000066\] {
  background-color: #00000066;
}
.bg-\[\#00000099\] {
  background-color: #00000099;
}
.bg-transparent {
  background-color: transparent;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.pb-px {
  padding-bottom: 1px;
}
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.font-\[\'Inter\'\] {
  font-family: 'Inter';
}
.text-\[10px\] {
  font-size: 10px;
}
.text-\[12px\] {
  font-size: 12px;
}
.text-\[14px\] {
  font-size: 14px;
}
.text-\[18px\] {
  font-size: 18px;
}
.text-\[24px\] {
  font-size: 24px;
}
.leading-\[10px\] {
  --tw-leading: 10px;
  line-height: 10px;
}
.leading-\[12px\] {
  --tw-leading: 12px;
  line-height: 12px;
}
.leading-\[14px\] {
  --tw-leading: 14px;
  line-height: 14px;
}
.leading-\[18px\] {
  --tw-leading: 18px;
  line-height: 18px;
}
.leading-\[20px\] {
  --tw-leading: 20px;
  line-height: 20px;
}
.leading-\[24px\] {
  --tw-leading: 24px;
  line-height: 24px;
}
.font-\[400\] {
  --tw-font-weight: 400;
  font-weight: 400;
}
.font-\[500\] {
  --tw-font-weight: 500;
  font-weight: 500;
}
.break-words {
  overflow-wrap: break-word;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.italic {
  font-style: italic;
}
.outline {
  outline-style: var(--tw-outline-style);
  outline-width: 1px;
}
.outline-none {
  --tw-outline-style: none;
  outline-style: none;
}
.group-focus-within\/3816e3b5\:border-2 {
  &:is(:where(.group\/3816e3b5):focus-within *) {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
}
.group-focus-within\/3816e3b5\:border-solid {
  &:is(:where(.group\/3816e3b5):focus-within *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-focus-within\/4ec05ee8\:border {
  &:is(:where(.group\/4ec05ee8):focus-within *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
}
.group-focus-within\/4ec05ee8\:border-solid {
  &:is(:where(.group\/4ec05ee8):focus-within *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-focus-within\/bb88f90b\:border {
  &:is(:where(.group\/bb88f90b):focus-within *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
}
.group-focus-within\/bb88f90b\:border-solid {
  &:is(:where(.group\/bb88f90b):focus-within *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-focus-within\/be48ca43\:border {
  &:is(:where(.group\/be48ca43):focus-within *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
}
.group-focus-within\/be48ca43\:border-solid {
  &:is(:where(.group\/be48ca43):focus-within *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-hover\/4ec05ee8\:border {
  &:is(:where(.group\/4ec05ee8):hover *) {
    @media (hover: hover) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
}
.group-hover\/4ec05ee8\:border-solid {
  &:is(:where(.group\/4ec05ee8):hover *) {
    @media (hover: hover) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
}
.group-hover\/969e345b\:hidden {
  &:is(:where(.group\/969e345b):hover *) {
    @media (hover: hover) {
      display: none;
    }
  }
}
.group-hover\/a4ee726a\:underline {
  &:is(:where(.group\/a4ee726a):hover *) {
    @media (hover: hover) {
      text-decoration-line: underline;
    }
  }
}
.group-hover\/bb88f90b\:border {
  &:is(:where(.group\/bb88f90b):hover *) {
    @media (hover: hover) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
}
.group-hover\/bb88f90b\:border-solid {
  &:is(:where(.group\/bb88f90b):hover *) {
    @media (hover: hover) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
}
.group-hover\/be48ca43\:border {
  &:is(:where(.group\/be48ca43):hover *) {
    @media (hover: hover) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
}
.group-hover\/be48ca43\:border-solid {
  &:is(:where(.group\/be48ca43):hover *) {
    @media (hover: hover) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
}
.group-active\/0f804ad9\:border-2 {
  &:is(:where(.group\/0f804ad9):active *) {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
}
.group-active\/0f804ad9\:border-solid {
  &:is(:where(.group\/0f804ad9):active *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-active\/3816e3b5\:border-2 {
  &:is(:where(.group\/3816e3b5):active *) {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
}
.group-active\/3816e3b5\:border-solid {
  &:is(:where(.group\/3816e3b5):active *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-disabled\/0f804ad9\:border-2 {
  &:is(:where(.group\/0f804ad9):disabled *) {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
}
.group-disabled\/0f804ad9\:border-solid {
  &:is(:where(.group\/0f804ad9):disabled *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-active\/0f804ad9\:group-disabled\/0f804ad9\:border-2 {
  &:is(:where(.group\/0f804ad9):active *) {
    &:is(:where(.group\/0f804ad9):disabled *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
}
.group-active\/0f804ad9\:group-disabled\/0f804ad9\:border-solid {
  &:is(:where(.group\/0f804ad9):active *) {
    &:is(:where(.group\/0f804ad9):disabled *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
}
.group-disabled\/3816e3b5\:border-2 {
  &:is(:where(.group\/3816e3b5):disabled *) {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
}
.group-disabled\/3816e3b5\:border-solid {
  &:is(:where(.group\/3816e3b5):disabled *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-active\/3816e3b5\:group-disabled\/3816e3b5\:border-2 {
  &:is(:where(.group\/3816e3b5):active *) {
    &:is(:where(.group\/3816e3b5):disabled *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
}
.group-active\/3816e3b5\:group-disabled\/3816e3b5\:border-solid {
  &:is(:where(.group\/3816e3b5):active *) {
    &:is(:where(.group\/3816e3b5):disabled *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
}
.group-disabled\/502d4919\:border-2 {
  &:is(:where(.group\/502d4919):disabled *) {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
}
.group-disabled\/502d4919\:border-solid {
  &:is(:where(.group\/502d4919):disabled *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-hover\/a4ee726a\:group-disabled\/a4ee726a\:no-underline {
  &:is(:where(.group\/a4ee726a):hover *) {
    @media (hover: hover) {
      &:is(:where(.group\/a4ee726a):disabled *) {
        text-decoration-line: none;
      }
    }
  }
}
.group-disabled\/de0b4dfb\:border-2 {
  &:is(:where(.group\/de0b4dfb):disabled *) {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
}
.group-disabled\/de0b4dfb\:border-solid {
  &:is(:where(.group\/de0b4dfb):disabled *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-aria-\[checked\=true\]\/0f804ad9\:flex {
  &:is(:where(.group\/0f804ad9)[aria-checked="true"] *) {
    display: flex;
  }
}
.group-aria-\[checked\=true\]\/0f804ad9\:border-2 {
  &:is(:where(.group\/0f804ad9)[aria-checked="true"] *) {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
}
.group-aria-\[checked\=true\]\/0f804ad9\:border-solid {
  &:is(:where(.group\/0f804ad9)[aria-checked="true"] *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-aria-\[checked\=true\]\/3816e3b5\:inline-flex {
  &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
    display: inline-flex;
  }
}
.group-aria-\[checked\=true\]\/3816e3b5\:border {
  &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
}
.group-aria-\[checked\=true\]\/3816e3b5\:border-solid {
  &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-aria-\[checked\=true\]\/3816e3b5\:font-\[\'Inter\'\] {
  &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
    font-family: 'Inter';
  }
}
.group-aria-\[checked\=true\]\/3816e3b5\:text-\[14px\] {
  &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
    font-size: 14px;
  }
}
.group-aria-\[checked\=true\]\/3816e3b5\:leading-\[14px\] {
  &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
    --tw-leading: 14px;
    line-height: 14px;
  }
}
.group-aria-\[checked\=true\]\/3816e3b5\:font-\[600\] {
  &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
    --tw-font-weight: 600;
    font-weight: 600;
  }
}
.group-focus-within\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-2 {
  &:is(:where(.group\/3816e3b5):focus-within *) {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
}
.group-focus-within\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-solid {
  &:is(:where(.group\/3816e3b5):focus-within *) {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
}
.group-active\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-2 {
  &:is(:where(.group\/3816e3b5):active *) {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      border-style: var(--tw-border-style);
      border-width: 2px;
    }
  }
}
.group-active\/3816e3b5\:group-aria-\[checked\=true\]\/3816e3b5\:border-solid {
  &:is(:where(.group\/3816e3b5):active *) {
    &:is(:where(.group\/3816e3b5)[aria-checked="true"] *) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
}
.group-aria-\[checked\=true\]\/502d4919\:flex {
  &:is(:where(.group\/502d4919)[aria-checked="true"] *) {
    display: flex;
  }
}
.group-aria-\[checked\=true\]\/502d4919\:border-2 {
  &:is(:where(.group\/502d4919)[aria-checked="true"] *) {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }
}
.group-aria-\[checked\=true\]\/502d4919\:border-solid {
  &:is(:where(.group\/502d4919)[aria-checked="true"] *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-aria-\[checked\=true\]\/de0b4dfb\:inline-flex {
  &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
    display: inline-flex;
  }
}
.group-aria-\[checked\=true\]\/de0b4dfb\:border {
  &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
}
.group-aria-\[checked\=true\]\/de0b4dfb\:border-solid {
  &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.group-aria-\[checked\=true\]\/de0b4dfb\:font-\[\'Inter\'\] {
  &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
    font-family: 'Inter';
  }
}
.group-aria-\[checked\=true\]\/de0b4dfb\:text-\[16px\] {
  &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
    font-size: 16px;
  }
}
.group-aria-\[checked\=true\]\/de0b4dfb\:leading-\[16px\] {
  &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
    --tw-leading: 16px;
    line-height: 16px;
  }
}
.group-aria-\[checked\=true\]\/de0b4dfb\:font-\[400\] {
  &:is(:where(.group\/de0b4dfb)[aria-checked="true"] *) {
    --tw-font-weight: 400;
    font-weight: 400;
  }
}
.group-data-\[state\=checked\]\/969e345b\:inline-flex {
  &:is(:where(.group\/969e345b)[data-state="checked"] *) {
    display: inline-flex;
  }
}
.group-data-\[state\=open\]\/d2e81e20\:h-auto {
  &:is(:where(.group\/d2e81e20)[data-state="open"] *) {
    height: auto;
  }
}
.group-data-\[state\=open\]\/d2e81e20\:w-full {
  &:is(:where(.group\/d2e81e20)[data-state="open"] *) {
    width: 100%;
  }
}
.group-data-\[state\=open\]\/d2e81e20\:flex-none {
  &:is(:where(.group\/d2e81e20)[data-state="open"] *) {
    flex: none;
  }
}
.group-\[\.outside\]\:bg-transparent {
  &:is(:where(.group):is(.outside) *) {
    background-color: transparent;
  }
}
.group-\[\.outside\.range-end\]\:rounded-l-none {
  &:is(:where(.group):is(.outside.range-end) *) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
.group-\[\.outside\.range-start\]\:rounded-r-none {
  &:is(:where(.group):is(.outside.range-start) *) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}
.group-\[\.range-middle\.selected\]\:rounded-none {
  &:is(:where(.group):is(.range-middle.selected) *) {
    border-radius: 0;
  }
}
.hover\:border {
  &:hover {
    @media (hover: hover) {
      border-style: var(--tw-border-style);
      border-width: 1px;
    }
  }
}
.hover\:border-b-2 {
  &:hover {
    @media (hover: hover) {
      border-bottom-style: var(--tw-border-style);
      border-bottom-width: 2px;
    }
  }
}
.hover\:border-none {
  &:hover {
    @media (hover: hover) {
      --tw-border-style: none;
      border-style: none;
    }
  }
}
.hover\:border-solid {
  &:hover {
    @media (hover: hover) {
      --tw-border-style: solid;
      border-style: solid;
    }
  }
}
.hover\:bg-\[\#ffffff29\] {
  &:hover {
    @media (hover: hover) {
      background-color: #ffffff29;
    }
  }
}
.hover\:bg-transparent {
  &:hover {
    @media (hover: hover) {
      background-color: transparent;
    }
  }
}
.active\:border-none {
  &:active {
    --tw-border-style: none;
    border-style: none;
  }
}
.active\:bg-\[\#ffffff3d\] {
  &:active {
    background-color: #ffffff3d;
  }
}
.active\:bg-transparent {
  &:active {
    background-color: transparent;
  }
}
.disabled\:cursor-default {
  &:disabled {
    cursor: default;
  }
}
.disabled\:border {
  &:disabled {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
}
.disabled\:border-solid {
  &:disabled {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.hover\:disabled\:cursor-default {
  &:hover {
    @media (hover: hover) {
      &:disabled {
        cursor: default;
      }
    }
  }
}
.hover\:disabled\:border {
  &:hover {
    @media (hover: hover) {
      &:disabled {
        border-style: var(--tw-border-style);
        border-width: 1px;
      }
    }
  }
}
.hover\:disabled\:border-solid {
  &:hover {
    @media (hover: hover) {
      &:disabled {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
}
.active\:disabled\:cursor-default {
  &:active {
    &:disabled {
      cursor: default;
    }
  }
}
.aria-\[checked\=true\]\:border {
  &[aria-checked="true"] {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }
}
.aria-\[checked\=true\]\:border-solid {
  &[aria-checked="true"] {
    --tw-border-style: solid;
    border-style: solid;
  }
}
.hover\:aria-\[checked\=true\]\:border {
  &:hover {
    @media (hover: hover) {
      &[aria-checked="true"] {
        border-style: var(--tw-border-style);
        border-width: 1px;
      }
    }
  }
}
.hover\:aria-\[checked\=true\]\:border-solid {
  &:hover {
    @media (hover: hover) {
      &[aria-checked="true"] {
        --tw-border-style: solid;
        border-style: solid;
      }
    }
  }
}
@property --tw-rotate-x {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-y {
  syntax: "*";
  inherits: false;
}
@property --tw-rotate-z {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-x {
  syntax: "*";
  inherits: false;
}
@property --tw-skew-y {
  syntax: "*";
  inherits: false;
}
@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@property --tw-leading {
  syntax: "*";
  inherits: false;
}
@property --tw-font-weight {
  syntax: "*";
  inherits: false;
}
@property --tw-outline-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}
@layer properties {
  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {
    *, ::before, ::after, ::backdrop {
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-outline-style: solid;
    }
  }
}