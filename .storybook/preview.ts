import type { Preview, Decorator } from '@storybook/react';

const isDev = process.env.NODE_ENV !== 'production';

const themeMap: Record<string, string> = isDev
	? {
			theme1: new URL('../src/themes/theme.css', import.meta.url).toString(),
			theme2: new URL('../src/themes/theme2.css', import.meta.url).toString(),
		}
	: {
			theme1: '/themes/theme1.css',
			theme2: '/themes/theme2.css',
		};

function applyThemeCss(theme: string) {
	const id = 'storybook-theme-style';
	let link = document.getElementById(id) as HTMLLinkElement | null;

	if (!link) {
		link = document.createElement('link');
		link.id = id;
		link.rel = 'stylesheet';
		document.head.appendChild(link);
	}

	link.href = themeMap[theme] || themeMap['theme1'];
}

function applyDarkMode(isDark: boolean) {
	const html = document.documentElement;
	html.classList.toggle('dark', isDark);
}

const withTheme: Decorator = (StoryFn, context) => {
	const selectedTheme = context.globals.theme || 'theme1';
	const isDark = context.globals.colorScheme === 'dark';

	applyThemeCss(selectedTheme);
	applyDarkMode(isDark);

	return StoryFn();
};

const preview: Preview = {
	parameters: {
		controls: {
			matchers: {
				color: /(background|color)$/i,
				date: /Date$/i,
			},
		},
	},
	globalTypes: {
		theme: {
			name: 'Theme',
			description: 'Select theme stylesheet',
			defaultValue: 'theme1',
			toolbar: {
				icon: 'paintbrush',
				items: [
					{ value: 'theme1', title: 'Theme 1' },
					{ value: 'theme2', title: 'Theme 2' },
				],
				showName: true,
				dynamicTitle: true,
			},
		},
		colorScheme: {
			name: 'Color scheme',
			description: 'Light or dark mode',
			defaultValue: 'light',
			toolbar: {
				icon: 'sun',
				items: [
					{ value: 'light', title: 'Light', icon: 'sun' },
					{ value: 'dark', title: 'Dark', icon: 'moon' },
				],
				showName: true,
				dynamicTitle: true,
			},
		},
	},
	decorators: [withTheme],
};

export default preview;
