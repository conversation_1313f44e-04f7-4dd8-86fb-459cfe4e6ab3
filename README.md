# Sportwrench Storybook

This Storybook contains reusable UI components for **Sportwrench** projects.

All components are developed with consistency and scalability in mind, aiming to support multiple products under the Sportwrench ecosystem.

Use this library as the single source of truth for styling, behavior, and interactions across applications.

## Package Information

- **Package Name**: `@sw-web/react-storybook`
- **Current Version**: 0.1.2
- **Registry**: GitLab Private Registry (Project ID: 231)
- **Registry URL**: https://gitlab.uastage.com/api/v4/projects/231/packages/npm/

## Development

### Building the Library

```bash
# Build the component library
npm run build

# Build Storybook static files
npm run build-storybook

# Run Storybook in development mode
npm run storybook
```

### Publishing

The project uses GitLab CI/CD for automatic publishing. When code is pushed to master, both Storybook and the package are automatically built and published.

**Steps:**
1. Manually bump version in `package.json`
2. Commit and push your changes to master
3. Pipeline automatically builds and publishes everything

### Local Testing

For local development and testing:

```bash
# Create .npmrc file for local testing
echo "registry=https://gitlab.uastage.com/api/v4/projects/231/packages/npm/" > .npmrc
echo "//gitlab.uastage.com/api/v4/projects/231/packages/npm/:_authToken=\${NPM_TOKEN}" >> .npmrc

# Set your GitLab token
export NPM_TOKEN=your_gitlab_token

# Build and test locally
npm run build
npm publish --dry-run  # Test without actually publishing
```

## Installation

To use this library in other projects, create a `.npmrc` file:

```
@sw-web:registry=https://gitlab.uastage.com/api/v4/groups/171/-/packages/npm/
//gitlab.uastage.com/api/v4/groups/171/-/packages/npm/:_authToken=gldt-Lo17oyYk2n25z4FgKCc9
//gitlab.uastage.com/api/v4/projects/231/packages/npm/:_authToken=gldt-Lo17oyYk2n25z4FgKCc9
```

Then install:
```bash
npm install @sw-web/react-storybook
```

## Authentication

You need a GitLab Personal Access Token with `api` scope:

1. Go to GitLab → User Settings → Access Tokens
2. Create a token with `api` scope
3. Set the environment variable: `export NPM_TOKEN=your_token_here`

## CI/CD Pipeline

The pipeline automatically:

1. **Build Stage**: Compiles the library and Storybook in parallel
2. **Deploy Stage**: Deploys Storybook static files to the server
3. **Publish Stage**: Publishes the package to GitLab registry
